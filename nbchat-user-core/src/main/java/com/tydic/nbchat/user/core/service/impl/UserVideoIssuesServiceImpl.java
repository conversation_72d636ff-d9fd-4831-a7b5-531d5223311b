package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.UserVideoIssuesApi;
import com.tydic.nbchat.user.api.bo.SendSmsRequest;
import com.tydic.nbchat.user.api.bo.eums.SmsHelperType;
import com.tydic.nbchat.user.api.bo.eums.SmsTemplateEnum;
import com.tydic.nbchat.user.api.bo.suggestion.UserSuggestionRspBO;
import com.tydic.nbchat.user.api.bo.suggestion.UserSuggestionStatsBO;
import com.tydic.nbchat.user.api.bo.suggestion.UserVideoIssuesBO;
import com.tydic.nbchat.user.api.bo.suggestion.UserVideoIssuesReqBo;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRechargeReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRefundReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.core.enmus.SuggestionStateEnum;
import com.tydic.nbchat.user.core.utils.NbchatSmsProxyHelper;
import com.tydic.nbchat.user.mapper.UserSuggestionMapper;
import com.tydic.nbchat.user.mapper.po.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 运营平台用户问题反馈
 */
@Slf4j
@Service
public class UserVideoIssuesServiceImpl implements UserVideoIssuesApi {
    @Resource
    private UserSuggestionMapper userSuggestionMapper;
    @Resource
    private TradeBalanceApi tradeBalanceApi;
    @Resource
    private NbchatSmsProxyHelper nbchatSmsProxyHelper;
    @Resource
    private UserBaseInfoApi userBaseInfoApi;

    /**
     * 运营平台用户视频问题反馈列表查询
     * @param queryReqBO
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> getVideoIssuesList(UserVideoIssuesReqBo queryReqBO) {
       log.info("运营平台用户视频问题反馈列表查询:{}",queryReqBO);
       UserVideoIssuesCondition condition = new UserVideoIssuesCondition();
       BeanUtils.copyProperties(queryReqBO,condition);
       Page<UserVideoIssuesSelectResult> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
       List<UserVideoIssuesSelectResult> resultList = userSuggestionMapper.selectWithCreationTask(condition);
       List<UserVideoIssuesBO> list = Lists.newArrayList();
       NiccCommonUtil.copyList(resultList,list,UserVideoIssuesBO.class);
        // 添加任务时长计算逻辑
        for (UserVideoIssuesBO bo : list) {
            if (bo.getStartTime() != null && bo.getEndTime() != null) {
                bo.setDuration((bo.getEndTime().getTime() - bo.getStartTime().getTime()) / 1000); // 单位：秒
            } else if (bo.getStartTime() != null) {
                bo.setDuration((System.currentTimeMillis() - bo.getStartTime().getTime()) / 1000); // 当前时间差
            }
        }
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    /**
     * 运营平台问题反馈列表查询
     * @param queryReqBO
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> getSuggestionList(UserVideoIssuesReqBo queryReqBO) {
        log.info("运营平台用户问题反馈列表查询:{}",queryReqBO);
        UserVideoIssuesCondition condition = new UserVideoIssuesCondition();
        BeanUtils.copyProperties(queryReqBO,condition);
        Page<UserVideoIssuesSelectResult> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
        List<UserVideoIssuesSelectResult> resultList = userSuggestionMapper.selectGetSuggestionList(condition);
        List<UserVideoIssuesBO> list = Lists.newArrayList();
        NiccCommonUtil.copyList(resultList,list,UserVideoIssuesBO.class);
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    /**
     * 用户反馈视频算力点消耗查询
     * @param
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> selectVideoCostByBizId(UserVideoIssuesReqBo queryReqBO) {
        log.info("用户反馈视频算力点消耗查询:{}",queryReqBO.getBizId());
       if(StringUtils.isEmpty(queryReqBO.getBizId())){
           return BaseRspUtils.createErrorRspList("bizId不能为空");
       }
        List<UserVideoCostResult> resultList = userSuggestionMapper.selectVideoCostByBizId(queryReqBO.getBizId());
        List<UserVideoIssuesBO> boList = Lists.newArrayList();
        NiccCommonUtil.copyList(resultList,boList,UserVideoIssuesBO.class);
        return BaseRspUtils.createSuccessRspList(boList);
    }

    /**
     * 当前算力点退还接口
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp adminAuditVideoIssues(UserVideoIssuesReqBo reqBO) {
        log.info("视频问题反馈审核并退还算力点, reqBO={}", reqBO);
        UserSuggestion currentSuggestion = userSuggestionMapper.selectById(reqBO.getSuggestId());
        // 校验当前状态是否为待查看状态
        if (!SuggestionStateEnum.PENDING.getCode().equals(currentSuggestion.getSuggestionState())) {
            return BaseRspUtils.createErrorRsp("只有待查看状态可操作");
        }
        String newStatus = reqBO.getSuggestionState();
        if (SuggestionStateEnum.ACCEPTED.getCode().equals(newStatus)) {
        if (reqBO.isNeedRefund()) {
            // 仅当 needRefund == true 时进行退款校验和操作
            if (reqBO.getScore() == null || reqBO.getScore() <= 0) {
                return BaseRspUtils.createErrorRsp("退还的算力点必须大于0");
            }
            // 查询当前任务消耗的算力点
            UserVideoIssuesReqBo costReq = new UserVideoIssuesReqBo();
            costReq.setBizId(reqBO.getBizId());
            RspList<UserVideoIssuesBO> costRsp = this.selectVideoCostByBizId(costReq);
            // 确保退款积分不大于已消耗积分
            if (!costRsp.isSuccess() || CollectionUtils.isEmpty(costRsp.getRows())) {
                return BaseRspUtils.createErrorRsp("未查询到该任务的消耗记录");
            }
            Integer totalConsumedScore = costRsp.getRows().get(0).getScore();
            if (totalConsumedScore == null) {
                return BaseRspUtils.createErrorRsp("任务消耗积分数据异常");
            }
            if (reqBO.getScore() > totalConsumedScore) {
                return BaseRspUtils.createErrorRsp("退还的算力点不能大于已消耗的算力点");
            }
            // 构造退款请求
            UserBalanceRefundReqBO refundReqBO = new UserBalanceRefundReqBO();
            refundReqBO.setBizId(reqBO.getBizId());
            refundReqBO.setTradeId(reqBO.getTradeId());
            refundReqBO.setRefundScore(reqBO.getScore());
            refundReqBO.setRemark(reqBO.getRemark());
            // 调用退款接口
            Rsp<UserTradeResult> refundResult = tradeBalanceApi.refund(refundReqBO);
            if (!refundResult.isSuccess()) {
                return BaseRspUtils.createErrorRsp("退款失败");
            }
            UserVideoIssuesBO smsBO = new UserVideoIssuesBO();
            smsBO.setPhone(reqBO.getPhone());
            smsBO.setRefundScore(reqBO.getScore());
            smsBO.setCreationName(reqBO.getCreationName());
            this.sendRefundSuccessSms(smsBO);
        }
        // 更新状态为“已采纳”
        UserSuggestion userSuggestion = new UserSuggestion();
        userSuggestion.setSuggestId(reqBO.getSuggestId());
        userSuggestion.setSuggestionState(SuggestionStateEnum.ACCEPTED.getCode());
        userSuggestion.setAuditTime(new Date());
        userSuggestion.setOrderNo(reqBO.getTradeId());
        int updateSuccess = userSuggestionMapper.updateSuggestion(userSuggestion);
        if (updateSuccess <= 0) {
            return BaseRspUtils.createErrorRsp("状态更新失败");
        }
        return BaseRspUtils.createSuccessRsp("操作成功");
    } else if (SuggestionStateEnum.EXPIRED.getCode().equals(newStatus)) {
        UserSuggestion userSuggestion = new UserSuggestion();
        userSuggestion.setSuggestId(reqBO.getSuggestId());
        userSuggestion.setSuggestionState(SuggestionStateEnum.EXPIRED.getCode());
        userSuggestion.setAuditTime(new Date());
        userSuggestionMapper.updateSuggestion(userSuggestion);
    }
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 管理员审核意见反馈赠送算力点
     * @param reqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp adminGiveVideoIssues(UserVideoIssuesReqBo reqBO) {
        log.info("管理员赠送视频问题反馈算力点, reqBO={}", reqBO);
        // 根据 suggestId 查询反馈信息
        UserSuggestion suggestion = userSuggestionMapper.selectById(reqBO.getSuggestId());
        if (suggestion == null) {
            return BaseRspUtils.createErrorRsp("未找到对应的反馈记录");
        }
        // 校验当前状态是否为“待采纳”
        if (!SuggestionStateEnum.PENDING.getCode().equals(suggestion.getSuggestionState())) {
            return BaseRspUtils.createErrorRsp("只有待处理状态的反馈才可以操作");
        }
        String newStatus = reqBO.getSuggestionState();
        if (SuggestionStateEnum.ACCEPTED.getCode().equals(newStatus)) {
            if (reqBO.isNeedGive()) {
                // 仅当 needGive == true 时进行赠送校验和操作
                if (reqBO.getScore() == null || reqBO.getScore() <= 0) {
                    return BaseRspUtils.createErrorRsp("赠送的算力点必须大于0");
                }
                // 构造充值请求参数
                UserBalanceRechargeReqBO rechargeReqBO = new UserBalanceRechargeReqBO();
                rechargeReqBO.setTenantCode(suggestion.getTenantCode());
                rechargeReqBO.setUserId(suggestion.getUserId());
                rechargeReqBO.setScore(reqBO.getScore());
                rechargeReqBO.setBizId(suggestion.getBusiId());
                rechargeReqBO.setExpireTime(reqBO.getExpireTime());
                rechargeReqBO.setRemark(reqBO.getRemark());
                // 调用充值接口
                Rsp<UserTradeResult> rechargeResult = tradeBalanceApi.recharge(rechargeReqBO);
                if (!rechargeResult.isSuccess()) {
                    return BaseRspUtils.createErrorRsp("赠送失败：" + rechargeResult.getRspDesc());
                }
                sendGiveSuccessSms(suggestion, reqBO.getScore());
            }
            // 更新反馈状态为“已采纳”
            UserSuggestion updateParam = new UserSuggestion();
            updateParam.setSuggestId(reqBO.getSuggestId());
            updateParam.setSuggestionState(SuggestionStateEnum.ACCEPTED.getCode());
            updateParam.setAuditTime(new Date());
            int updateCount = userSuggestionMapper.updateSuggestion(updateParam);
            if (updateCount <= 0) {
                return BaseRspUtils.createErrorRsp("状态更新失败");
            }
            return BaseRspUtils.createSuccessRsp("操作成功");
        }
        if (SuggestionStateEnum.EXPIRED.getCode().equals(newStatus) ||
                SuggestionStateEnum.PROCESSED.getCode().equals(newStatus)) {
            UserSuggestion updateParam = new UserSuggestion();
            updateParam.setSuggestId(reqBO.getSuggestId());
            updateParam.setSuggestionState(newStatus);
            updateParam.setAuditTime(new Date());

            userSuggestionMapper.updateSuggestion(updateParam);
            return BaseRspUtils.createSuccessRsp("操作成功");
        }
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 视频上报、意见反馈、审核状态近三十天统计
     * @param reqBO
     * @return
     */
    @Override
    public Rsp<UserSuggestionRspBO> selectSuggestionStats(UserVideoIssuesReqBo reqBO) {
        log.info("视频上报、意见反馈、审核状态近三十天统计, reqBO={}", reqBO);
        UserSuggestionRspBO repVo = new UserSuggestionRspBO();
        UserVideoIssuesCondition condition = new UserVideoIssuesCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setDays(reqBO.getDays());
        condition.setProductModule(reqBO.getProductModule());
        condition.setPageSource(reqBO.getPageSource());
        List<UserSuggestionStatsPo> statsList = userSuggestionMapper.selectSuggestionStats(condition);
        List<UserSuggestionStatsBO> boList = Lists.newArrayList();
        NiccCommonUtil.copyList(statsList, boList, UserSuggestionStatsBO.class);
        loadReportData(boList, repVo);
        return BaseRspUtils.createSuccessRsp(repVo);
    }

    /**
     * 组装统计数据
     */
    private void loadReportData(List<UserSuggestionStatsBO> list, UserSuggestionRspBO repVo) {
        if (!CollectionUtils.isEmpty(list)) {
            Integer totalCount = 0;
            for (UserSuggestionStatsBO item : list) {
                totalCount += item.getRecordCount();
            }
            repVo.setTotalRecords(totalCount);

            DecimalFormat df = new DecimalFormat("0.00");
            List<UserSuggestionStatsBO> result = new ArrayList<>();
            for (UserSuggestionStatsBO item : list) {
                double percentage = (totalCount != 0) ? ((double) item.getRecordCount() / totalCount) * 100 : 0.0;
                item.setPercentage(df.format(percentage) + "%");
                result.add(item);
            }
            repVo.setItems(result);
        }
    }

    /**
     * 退还算力点成功短信通知
     *
     */
    private void sendRefundSuccessSms(UserVideoIssuesBO bo) {
        String phone = bo.getPhone();
        String creationName = bo.getCreationName();
        Integer refundScore = bo.getRefundScore();

        if (StringUtils.isBlank(phone) || StringUtils.isBlank(creationName) || refundScore == null || refundScore <= 0) {
            log.warn("参数不完整，无法发送短信");
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("creationName", creationName);
        params.put("refundScore", refundScore.toString());

        SendSmsRequest smsReq = SendSmsRequest.builder()
                .templateCode(SmsTemplateEnum.VIDEO_ISSUES_AUDIT_REFUND.getTemplateID())
                .phone(phone)
                .signName("课件帮")
                .templateParam(params)
                .build();
        Rsp smsResult = nbchatSmsProxyHelper.send(SmsHelperType.ALI, smsReq);
        if (!smsResult.isSuccess()) {
            log.error("短信发送失败，手机号：{}", smsReq.getPhone());
        }
    }

    /**
     * 赠送算力点成功短信通知
     * @param suggestion 用户反馈信息
     * @param giveScore  赠送的算力点数
     */
    private void sendGiveSuccessSms(UserSuggestion suggestion, Integer giveScore) {
        // 获取用户ID和租户编码
        String userId = suggestion.getUserId();
        String tenantCode = suggestion.getTenantCode();
        Rsp<UserBaseInfo> userBaseInfoRsp = userBaseInfoApi.getByUserId(tenantCode, userId);

        UserBaseInfo userBaseInfo = userBaseInfoRsp.getData();
        String phone = userBaseInfo.getPhone();

        if (StringUtils.isBlank(phone)) {
            log.error("用户手机号为空，userId={}, tenantCode={}", userId, tenantCode);
            return;
        }

        Map<String, String> params = new HashMap<>();
        params.put("score", giveScore.toString());

        // 发送短信
        SendSmsRequest smsReq = SendSmsRequest.builder()
                .templateCode(SmsTemplateEnum.VIDEO_ISSUES_GIVE.getTemplateID())
                .phone(phone)
                .signName("课件帮")
                .templateParam(params)
                .build();

        Rsp smsResult = nbchatSmsProxyHelper.send(SmsHelperType.ALI, smsReq);
        if (!smsResult.isSuccess()) {
            log.error("赠送短信发送失败，手机号：{}", smsReq.getPhone());
        }
    }
}
