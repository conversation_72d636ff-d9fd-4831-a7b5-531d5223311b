package com.tydic.nbchat.user.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.NbchatUserRegLoginApi;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.BindPhoneNumberReqBO;
import com.tydic.nbchat.user.api.bo.ChangePhoneNumberReqBO;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.auth.AuthUserRsp;
import com.tydic.nbchat.user.api.bo.auth.UserTokenInfo;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.AuthType;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.login.UserLoginReqBO;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nbchat.user.api.bo.utils.RedisKeyUtils;
import com.tydic.nbchat.user.api.bo.vip.SuYanAuthCodeReqBO;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import com.tydic.nbchat.user.core.config.SuyanConfigProperties;
import com.tydic.nbchat.user.core.utils.EncryptionUtil;
import com.tydic.nbchat.user.core.utils.HttpRequestUtil;
import com.tydic.nbchat.user.core.utils.SuyanSignatureUtils;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.JWTUtils;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class UserAuthBusiService {

    private final UserSettingHelper userSettingHelper;
    private final NbchatUserApi nbchatUserApi;
    private final NbchatUserConfigProperties nbchatUserConfigProperties;
    private final RedisHelper redisHelper;
    private final NbchatUserRegLoginApi nbchatUserRegLoginApi;
    private final UserInfoBusiService userInfoBusiService;
    private final VerifyPhoneCodeBusiService verifyPhoneCodeBusiService;
    private final SuyanConfigProperties suyanConfigProperties;
    private final UserSettingsApi userSettingsApi;

    public static String HEADER_TOKEN_KEY = "auth-token";

    public UserAuthBusiService(UserSettingHelper userSettingHelper,
                               NbchatUserApi nbchatUserApi,
                               NbchatUserConfigProperties nbchatUserConfigProperties,
                               RedisHelper redisHelper,
                               NbchatUserRegLoginApi nbchatUserRegLoginApi,
                               UserInfoBusiService userInfoBusiService,
                               VerifyPhoneCodeBusiService verifyPhoneCodeBusiService,
                               SuyanConfigProperties suyanConfigProperties,
                               UserSettingsApi userSettingsApi) {
        this.userSettingHelper = userSettingHelper;
        this.nbchatUserApi = nbchatUserApi;
        this.nbchatUserConfigProperties = nbchatUserConfigProperties;
        this.redisHelper = redisHelper;
        this.nbchatUserRegLoginApi = nbchatUserRegLoginApi;
        this.userInfoBusiService = userInfoBusiService;
        this.verifyPhoneCodeBusiService = verifyPhoneCodeBusiService;
        this.suyanConfigProperties = suyanConfigProperties;
        this.userSettingsApi = userSettingsApi;
    }

    /**
     * 更换手机号
     *
     * @param reqBO
     * @return
     */
    public Rsp changePhone(ChangePhoneNumberReqBO reqBO) {
        boolean checkCode = verifyPhoneCodeBusiService.verifyPhoneCode(reqBO.getPhone(), reqBO.getCode());
        if (!checkCode) {
            return BaseRspUtils.createErrorRsp("验证码错误");
        }
        userInfoBusiService.updatePhone(reqBO.getUserId(), reqBO.getPhone());
        return BaseRspUtils.createSuccessRsp(reqBO.getUserId(), "手机号成功，请重新登录");
    }

    public Rsp<AuthUserRsp> login(AuthUserReqBO authUserReqBO, HttpServletRequest request, HttpServletResponse response) {
        log.info("用户登录-参数: {}", authUserReqBO);
        Rsp loginRsp;
        if (AuthType.USER_PASS.getCode().equals(authUserReqBO.getAuthType())) {
            loginRsp = loginByPassword(authUserReqBO);
        } else {
            loginRsp = nbchatUserApi.authUser(authUserReqBO);
        }
        if (loginRsp.isSuccess()) {
            NbchatUserInfo userInfo = (NbchatUserInfo) loginRsp.getData();
            return auth(userInfo, request, response);
        }
        return loginRsp;
    }

    private Rsp loginByPassword(AuthUserReqBO authUserReqBO) {
        if (StringUtils.isAllBlank(authUserReqBO.getUsername(), authUserReqBO.getPassword())) {
            return BaseRspUtils.createErrorRsp("用户名密码不得为空！");
        }
        UserLoginReqBO loginReqBO = new UserLoginReqBO();
        loginReqBO.setUsername(authUserReqBO.getUsername());
        String password = EncryptionUtil.decryptField(authUserReqBO.getPassword());
        loginReqBO.setPassword(password);
        loginReqBO.setCaptchaCode(authUserReqBO.getCaptchaCode());
        loginReqBO.setLoginIp(authUserReqBO.getLoginIp());
        loginReqBO.setLoginUa(authUserReqBO.getLoginUa());
        return nbchatUserRegLoginApi.login(loginReqBO);
    }

    /**
     * 绑定手机号
     *
     * @param bindPhoneNumber
     * @param request
     * @param response
     * @return
     */
    public Rsp bindPhone(BindPhoneNumberReqBO bindPhoneNumber,
                         HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("用户绑定手机-开始:{}", bindPhoneNumber);
            Rsp<NbchatUserInfo> authRsp = nbchatUserApi.bindPhoneNumber(bindPhoneNumber);
            log.info("用户绑定手机-结果:{}", authRsp);
            if (authRsp.isSuccess()) {
                return auth(authRsp.getData(), request, response);
            }
            return authRsp;
        } catch (Exception e) {
            log.error("用户绑定手机-异常:{}", bindPhoneNumber, e);
            return BaseRspUtils.createErrorRsp("服务器内部错误,请稍候重试！");
        }
    }

    public NbchatUserInfo getUserInfo(String userId, String platform) {
        return userInfoBusiService.getUserInfo(userId, platform);
    }

    public NbchatUserInfo getUserInfo(String userId) {
        return userInfoBusiService.getUserInfo(userId);
    }

    public String getAuthToken(HttpServletRequest request) {
        log.info("获取token请求头={}", HEADER_TOKEN_KEY);
        String token = request.getHeader(HEADER_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            token = request.getParameter(HEADER_TOKEN_KEY);
            if (StringUtils.isBlank(token)) {
                Cookie[] cookies = request.getCookies();
                if (cookies != null) {
                    for (Cookie cookie : cookies) {
                        if (cookie.getName().equals(HEADER_TOKEN_KEY)) {
                            token = cookie.getValue();
                            break;
                        }
                    }
                }
            }
        }
        return token;
    }

    /**
     * 微信扫码登录检查
     *
     * @param sessionKey
     * @param response
     * @return
     */
    public NbchatUserInfo loginCheck(String sessionKey, HttpServletResponse response) {
        JSONObject jsonObject = (JSONObject) redisHelper.get(RedisKeyUtils.getQrcodeKey(sessionKey));
        if (ObjectUtils.isEmpty(jsonObject)) {
            return null;
        }
        NbchatUserInfo userInfo = (NbchatUserInfo) jsonObject.get("userInfo");
        if (ObjectUtils.isNotEmpty(userInfo)) {
            authByJwt(userInfo, response);
        }
        return userInfo;
    }

    /**
     * 对接粤问登录检查
     *
     * @param data     流水号
     * @param response
     * @return
     */
    public Rsp<NbchatUserInfo> yuwenLoginCheck(String data, HttpServletResponse response) {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(nbchatUserConfigProperties.getYuewenLoginUrl()).build(data).toString();
            log.info("粤问登录检查-开始: {}", data);
            String res = HttpClientHelper.doGet(url, new HashMap<>(), 1000, 5000);
            log.info("粤问登录检查-结果: {}|{}", data, res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            //获取手机号，根据手机号查询用户信息
            /**
             * {
             * 	"success": true,
             * 	"message": "",
             * 	"code": 200,
             * 	"result": {
             * 		"mobileNo": "13333333333"
             *        },
             * 	"timestamp": 1711505655329
             * }
             */
            if (!"200".equals(jsonObject.getString("code"))) {
                return BaseRspUtils.createErrorRsp("登录失败：" + jsonObject.getString("message"));
            }
            String phone = jsonObject.getJSONObject("result").getString("mobileNo");
            Rsp<NbchatUserInfo> userInfoRsp = userInfoBusiService.getUserByPhone(phone);
            if (userInfoRsp.isSuccess()) {
                NbchatUserInfo userInfo = userInfoRsp.getData();
                authByJwt(userInfo, response);
                return BaseRspUtils.createSuccessRsp(userInfo, "登录成功");
            }
            return BaseRspUtils.createErrorRsp("登录失败：用户不存在");
        } catch (Exception e) {
            log.error("粤问登录检查-异常: {}", data, e);
            return BaseRspUtils.createErrorRsp("登录失败：服务器内部错误,请稍候重试！");
        }
    }

    /**
     * 用户退出登录
     *
     * @param userInfo
     * @param token
     */
    public void authLogout(UserTokenInfo userInfo, String token) {
        try {
            userSettingHelper.cleanUserCache(userInfo.getTenantCode(), userInfo.getUserId());
            String tokenMd5 = MD5Utils.md5Hex(token.getBytes());
            String key = RedisConstants.USER_TOKEN_DISABLE_PREFIX_KEY + tokenMd5;
            long exp = TimeUnit.DAYS.toSeconds(3);
            log.info("用户退出登录-标记token: {}", key);
            redisHelper.set(key, System.currentTimeMillis(), exp);
        } catch (NoSuchAlgorithmException e) {
            log.warn("token 计算md5异常:", e);
        }
    }

    /**
     * 强制退出登录
     *
     * @param userId
     */
    public void authLogout(String userId) {
        userSettingHelper.removeInfo(userId);
        //获取当前登录的sessionKey
        String cacheKey = RedisConstants.USER_SESSION_KEY_PREFIX + userId;
        String sessionKey = (String) redisHelper.get(cacheKey);
        if (StringUtils.isNotBlank(sessionKey)) {
            //移除用户子系统信息缓存
            userSettingHelper.cleanUserCache(UserAttributeConstants.DEFAULT_TENANT_CODE, userId);
            String key = RedisConstants.USER_TOKEN_DISABLE_PREFIX_KEY + sessionKey;
            long exp = TimeUnit.DAYS.toSeconds(3);
            log.info("用户退出登录-标记token: {}|{}", userId, key);
            redisHelper.set(key, System.currentTimeMillis(), exp);
        }
    }

    private Rsp<AuthUserRsp> auth(NbchatUserInfo userInfo, HttpServletRequest request, HttpServletResponse response) {
        String pageUrl = request.getParameter("pageUrl");
        if (StringUtils.isBlank(pageUrl)) {
            pageUrl = nbchatUserConfigProperties.getAuth().getMainPage();
        } else {
            pageUrl = HttpRequestUtil.urlDecode(pageUrl);
        }
        //2.生成token
        String authToken = authByJwt(userInfo, response);
        //3.重定向页面
        AuthUserRsp authUserRsp = AuthUserRsp.builder().msg("登录成功！").
                redirect(pageUrl).authToken(authToken).status(userInfo.getStatus()).build();
        //移除用户子系统信息缓存
        userSettingHelper.cleanUserCache(userInfo.getTenantCode(), userInfo.getUserId());
        log.warn("用户认证-正式用户跳转:{}->{}", userInfo, pageUrl);
        //userRoleDeptApi.getUserSubSysInfo(userInfo.getUserId(), userInfo.getTenantCode());
        return BaseRspUtils.createSuccessRsp(authUserRsp);
    }

    public String authByJwt(NbchatUserInfo userInfo, HttpServletResponse response) {
        String loginInfo = UserTokenInfo.of(userInfo.getUserId(), userInfo.getTenantCode());
        Integer sessionTime = nbchatUserConfigProperties.getAuth().getSessionTimeout();
        String token = JWTUtils.createJwtToken(userInfo.getUserId(), loginInfo, sessionTime * 1000L);
        Cookie cookie = new Cookie(HEADER_TOKEN_KEY, token);
        cookie.setPath("/");
        cookie.setMaxAge(sessionTime);
        try {
            String cacheKey = RedisConstants.USER_SESSION_KEY_PREFIX + userInfo.getUserId();
            redisHelper.set(cacheKey, MD5Utils.md5Hex(token.getBytes()), sessionTime);
        } catch (NoSuchAlgorithmException e) {
            log.warn("token 计算md5异常:", e);
        }
        response.addCookie(cookie);
        return token;
    }

    /**
     * 对接苏研云电脑AI秘书登录检查
     * 1、接收免登码、SessionID
     * 2、免登码获取token
     * 3、token获取云电脑手机号，用户名等信息
     * 4、判断手机号是否存在，存在登录，不存在注册后登录
     * @param reqBO
     * @param response
     * @return
     */
    public Rsp<NbchatUserInfo> suyanLoginCheck(SuYanAuthCodeReqBO reqBO, HttpServletResponse response) {
       if(!suyanConfigProperties.isEnable()){
           return BaseRspUtils.createErrorRsp("登录失败：未开启免登录！");
       }
        try {
            //校验入参（请求id、认证回话ID和免登码）
            String error ="";
            if(!verifySuyanParm(reqBO,error)){
                return BaseRspUtils.createErrorRsp(error);
            }
            //根据免登码获取token
            SuyanSignatureUtils signatureUtil = new SuyanSignatureUtils(suyanConfigProperties.getAccessKeyId(),suyanConfigProperties.getAccessKeySecret());
            String validateSignature = signatureUtil.doSignature(suyanConfigProperties.getValidateAuthCode(), "POST", new HashMap<>());
            Map<String, String> headers = new HashMap<>();
            headers.put("csa-request-id", reqBO.getRequestId());
            headers.put("csa-auth-session-id",reqBO.getAuthSessionId());
            JSONObject body = new JSONObject();
            body.put("appId", suyanConfigProperties.getAppId());
            body.put("appSecret",suyanConfigProperties.getAppSecret());
            body.put("authCode", reqBO.getAuthCode());
            long validateStart = System.currentTimeMillis();
            String validateResult = HttpClientHelper.doPost(suyanConfigProperties.getBaseUrl() + validateSignature, headers, body,5000,10000);
            log.info("苏研免登码检查-免登码检验结果: {}|接口耗时：{}", validateResult, (System.currentTimeMillis()-validateStart));
            if (StringUtils.isEmpty(validateResult)) {
                return BaseRspUtils.createErrorRsp("登录失败：校验免登码失败！"  );
            }
            JSONObject validateJsonObject = JSONObject.parseObject(validateResult);
            if (!"200".equals(validateJsonObject.getString("errorCode"))) {
                return BaseRspUtils.createErrorRsp("登录失败：" + validateJsonObject.getString("errorMessage"));
            }
            //获取云电脑用户信息
            Map<String, String> userHeaders = new HashMap<>();
            userHeaders.put("csa-request-id", reqBO.getRequestId());
            userHeaders.put("csa-auth-session-id",reqBO.getAuthSessionId());
            String accessToken = validateJsonObject.getJSONObject("body").getString("accessToken");
            userHeaders.put("csa-access-token", accessToken);
            String userSignature = signatureUtil.doSignature(suyanConfigProperties.getQueryEcloudComputerUser(), "POST", new HashMap<>());
            long beginTime = System.currentTimeMillis();
            String userResult = HttpClientHelper.doPost(suyanConfigProperties.getBaseUrl() + userSignature, userHeaders, "{}",5000,10000);
            log.info("苏研免登码检查-查询云电脑用户结果: {}|接口耗时：{}", userResult, (System.currentTimeMillis()-beginTime));
            //获取手机号，根据手机号查询用户信息
            /**
            * {
            *  "state": "OK",
            *      "body":
            *  {
            *     "ecloudUserId": "1932036254110388226",
            *     "ecloudUserMobile":"18500001111",
            *     "ecloudUserName": "test_tydk_ai"
            *    },
            *    "errorCode": "200",
            *    "errorMessage": "",
            *    "requestId": "reqId-8b383615644941f5aa189c6a56"
            *   }
            */
            if (StringUtils.isEmpty(userResult)) {
                return BaseRspUtils.createErrorRsp("登录失败：获取云电脑用户信息失败！"  );
            }
            JSONObject userJsonObject = JSONObject.parseObject(userResult);
            if (!"200".equals(userJsonObject.getString("errorCode"))) {
                return BaseRspUtils.createErrorRsp("登录失败：" + userJsonObject.getString("message"));
            }
            JSONObject bodyJSONObject = userJsonObject.getJSONObject("body");
            if(bodyJSONObject.containsKey("ecloudUserMobile")){
                String phone = bodyJSONObject.getString("ecloudUserMobile");
                if(!StringUtils.isEmpty(phone)){
                    //登录
                    Rsp<NbchatUserInfo> userInfoRsp = userInfoBusiService.getUserByPhone(phone);
                    if (userInfoRsp.isSuccess()) {
                        NbchatUserInfo userInfo = userInfoRsp.getData();
                        authByJwt(userInfo, response);
                        return BaseRspUtils.createSuccessRsp(userInfo, "登录成功");
                    }else{
                        //手机号不存在，注册新用户
                        UserRegistReqBO registReqBO = new UserRegistReqBO();
                        if (StringUtils.isBlank(registReqBO.getPassword())) {
                            registReqBO.setPassword(String.valueOf(NiccCommonUtil.generateRandomCode(8, 11)));
                        }
                        String ecloudUserName = bodyJSONObject.getString("ecloudUserName");
                        if (!StringUtils.isBlank(ecloudUserName)) {
                            registReqBO.setName(ecloudUserName);
                        }
                        registReqBO.setUsername(phone);
                        registReqBO.setPhone(phone);
                        registReqBO.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
                        registReqBO.setJoinType(JoinTenantType.SUYAN_E_CLOUD.getCode());
                        registReqBO.setClientUa(reqBO.getClientUa());
                        registReqBO.setClientIp(reqBO.getClientIp());
                        log.info("苏研免登码检查-手机号不存在-注册新用户-注册信息:{}", registReqBO);
                        Rsp<NbchatUserInfo> userRegRsp = nbchatUserRegLoginApi.regist(registReqBO);
                        if (userRegRsp.isSuccess()) {
                            NbchatUserInfo userInfo = userRegRsp.getData();
                            try {
                                userSettingsApi.removeUserCache(userInfo.getTenantCode(), userInfo.getUserId());
                            } catch (Exception e) {
                                log.error("同步外部用户信息-清除用户缓存失败: {}", userInfo, e);
                            }
                            authByJwt(userInfo, response);
                            return BaseRspUtils.createSuccessRsp(userInfo, "登录成功");
                        }
                    }
                }
            }
            return BaseRspUtils.createErrorRsp("免登录失败：获取手机号失败，请手动登录！");
        } catch (Exception e) {
            log.error("苏研免登码检查-异常: {}", reqBO, e);
            return BaseRspUtils.createErrorRsp("登录失败：服务器内部错误,请稍候重试！");
        }
    }
    /**
     * 校验入参
     * @param reqBO
     * @param errorMassage
     * @return
     */
    public boolean verifySuyanParm(SuYanAuthCodeReqBO reqBO, String errorMassage) {
        if (StringUtils.isEmpty(reqBO.getRequestId())) {
            errorMassage ="请求id 不能为空";
            return false;
        }
        if (StringUtils.isEmpty(reqBO.getAuthSessionId())) {
            errorMassage ="认证会话id不能为空";
            return false;
        }
        if (StringUtils.isEmpty(reqBO.getAuthCode())) {
            errorMassage = "免登码不能为空";
            return false;
        }
        return true;
    }
}
