package com.tydic.nbchat.user.core.busi;

import com.tydic.nbchat.user.api.bo.constants.DicDouConstants;
import com.tydic.nbchat.user.api.bo.eums.ScoreStatusType;
import com.tydic.nbchat.user.api.bo.eums.TradeType;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRechargeReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.mapper.NbchatUserBalanceMapper;
import com.tydic.nbchat.user.mapper.NbchatUserBillRecordMapper;
import com.tydic.nbchat.user.mapper.NbchatUserScoreDetailMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserBalance;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecord;
import com.tydic.nbchat.user.mapper.po.NbchatUserScoreDetail;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class ScoreRechargeService {

    @Resource
    private NbchatUserBalanceMapper nbchatUserBalanceMapper;
    @Resource
    private NbchatUserBillRecordMapper nbchatUserBillRecordMapper;
    @Resource
    private NbchatUserScoreDetailMapper nbchatUserScoreDetailMapper;

    private final ScoreBalanceService scoreBalanceService;

    private final UserMakeEventSender userMakeEventSender;

    public ScoreRechargeService(ScoreBalanceService scoreBalanceService,
                                UserMakeEventSender userMakeEventSender) {
        this.scoreBalanceService = scoreBalanceService;
        this.userMakeEventSender = userMakeEventSender;
    }

    @Transactional(rollbackFor = Exception.class)
    public Rsp<UserTradeResult> recharge(UserBalanceRechargeReqBO recharge) {
        log.info("余额充值-参数: {}", recharge);
        NbchatUserBalance balance = nbchatUserBalanceMapper.selectByUserId(recharge.getTenantCode(), recharge.getUserId());
        if (balance != null) {
            int score = recharge.getScore();
            if (score <= 0) {
                return BaseRspUtils.createErrorRsp("充值积分不能小于0");
            }
            recharge.setTradeTime(new Date());
            balance.setScore(balance.getScore() + score);
            balance.setUpdateTime(new Date());
            nbchatUserBalanceMapper.updateByPrimaryKeySelective(balance);

            //新增积分明细
            NbchatUserScoreDetail scoreDetail = new NbchatUserScoreDetail();
            scoreDetail.setTenantCode(recharge.getTenantCode());
            scoreDetail.setUserId(recharge.getUserId());
            scoreDetail.setScoreType(recharge.getScoreType());
            scoreDetail.setScore(score);
            scoreDetail.setCreateTime(new Date());
            scoreDetail.setScoreDesc(recharge.getRemark());
            scoreDetail.setExpireTime(recharge.getExpireTime());
            scoreDetail.setOrderNo(recharge.getOrderNo());
            if (StringUtils.isBlank(scoreDetail.getScoreDesc())) {
                scoreDetail.setScoreDesc(DicDouConstants.RECHARGE_DOU_REMARK);
            }
            if (recharge.getExpireTime() == null) {
                Date date = DateTimeUtil.DateAddYear(DicDouConstants.DEFAULT_EXPIRE_YEAR);
                scoreDetail.setExpireTime(DateTimeUtil.createTime(date, 23, 59, 59));
            }
            scoreDetail.setScoreStatus(ScoreStatusType.VALID.getCode());
            nbchatUserScoreDetailMapper.insertSelective(scoreDetail);

            // 记录交易流水
            NbchatUserBillRecord record = new NbchatUserBillRecord();
            BeanUtils.copyProperties(recharge, record);
            record.setScore(score);
            record.setType(TradeType.RECHARGE.getCode());
            if (StringUtils.isBlank(record.getTradeId())) {
                record.setTradeId(IdWorker.nextAutoIdStr());
            }
            if (StringUtils.isBlank(record.getRemark())) {
                record.setRemark(DicDouConstants.RECHARGE_DOU_REMARK);
            }
            nbchatUserBillRecordMapper.insertSelective(record);

            UserTradeResult result = new UserTradeResult();
            BeanUtils.copyProperties(record, result);
            Rsp<UserBalanceBO> balanceRsp = scoreBalanceService.getBalance(recharge.getTenantCode(), recharge.getUserId());
            result.setBalance(balanceRsp.getData());

            //发送算力点充值事件
            userMakeEventSender.sendScoreChangeEvent(result, balance.getScore());

            return BaseRspUtils.createSuccessRsp(result,"交易成功");
        }
        return BaseRspUtils.createErrorRsp("充值失败");
    }

}
