package com.tydic.nbchat.user.core.wx.base.handler;

import com.tydic.nbchat.user.core.enmus.WxBaseMsgTypeEnum;
import com.tydic.nbchat.user.core.wx.base.api.MessageHandler;
import com.tydic.nbchat.user.core.wx.context.WxMsgBaseContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LocationHandler implements MessageHandler {
    @Override
    public String msgType() {
        return WxBaseMsgTypeEnum.location.getCode();
    }

    @Override
    public <T extends WxMsgBaseContext> Rsp<?> handle(T eventMsg) {
        log.info("用户发送位置消息:{}",eventMsg);
        return BaseRspUtils.createSuccessRsp("");
    }
}
