package com.tydic.nbchat.user.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/12
 * @description 苏研云电脑单点配置属性
 */
@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "nbchat-user.config.suyan")
public class SuyanConfigProperties {
    private boolean enable = true;
    /**
     * 移动云账号-访问密钥id(AK)
     */
    private String accessKeyId = "";
    /**
     * 移动云账号-访问密钥秘密(SK)
     */
    private String accessKeySecret = "";
    /**
     * AppId
     */
    private String appId = "";
    /**
     *appSecret
     */
    private String appSecret = "";
    /**
     * 地址
     */
    private String baseUrl = "";
    /**
     * 校验免登码（端内应用免登）地址
     */
    private String validateAuthCode = "";
    /**
     * 续期认证票据（端内应用续期）
     */
    private String renewAuthToken = "";
    /**
     * 查询云电脑用户（端内应用查询用户）
     */
    private String queryEcloudComputerUser = "";


}
