package com.tydic.nbchat.user.core.config;

import com.tydic.nbchat.user.api.bo.constants.DicDouConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "nbchat-user.config")
public class NbchatUserConfigProperties {
    //是否允许外部注册用户
    private Boolean userRegEnable = true;
    //算力点积分扣费
    private Boolean scoreDeductEnable = true;
    //定时任务开关
    private Boolean enableTimer = true;
    private WchatConfigProperties wchat;
    private SmsConfigProperties sms;
    private String defaultAvatar = "https://chatfiles-test.tydiczt.com/files/202408/9f56eb5c1ba2dea308ac41abd1999924.png";
    private Map<String,Object> defaultUserSettings = new HashMap<>();
    //自动绑定用户设置链接
    private String bindSettingUrl = "https://chat.tydiczt.com/chat/?setRobot={code}";
    //绑定用户初始设置
    private Map<String,Object> bindSettingDefault = new HashMap<>();
    private CaptchaConfigProperties captcha;
    private UserAuthConfigProperties auth = new UserAuthConfigProperties();
    private String yuewenLoginUrl = "https://localhost/wecom-test/gePhoneByUniqueNo?uniqueNo={uniqueNo}";
    //新用户赠送豆
    private Integer newUserDou = DicDouConstants.NEW_PUBLIC_USER_GIVE_DOU;
    //自定义 auth-token key
    private String authTokenKey = "";
    //钉钉发送消息配置
    private String dingtalkTitle = "测试环境";
    private Boolean dingtalkEnable = true;
    private String dingtalkServerUrl = "https://oapi.dingtalk.com/robot/send?access_token=070bbb3aee863b4ff978e828f23954f5c81fa2a3016aee385be18308b7fb3b0b";
    private Map<String,String> dingtalkSendMap = new HashMap<>();
}
