package com.tydic.nbchat.user.core.utils;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class DingtalkUtil {

    static String dingtalkTitle = "";
    static Boolean dingtalkEnable = false;
    static String dingtalkServerUrl = "";
    static Map<String,String> robotSendMap = new HashMap<>();
    DingtalkUtil(NbchatUserConfigProperties nbchatUserConfigProperties) {
        dingtalkTitle = nbchatUserConfigProperties.getDingtalkTitle();
        dingtalkEnable = nbchatUserConfigProperties.getDingtalkEnable();
        dingtalkServerUrl = nbchatUserConfigProperties.getDingtalkServerUrl();
        robotSendMap = nbchatUserConfigProperties.getDingtalkSendMap();
    }

    public static void sendMessageSuggestion(String content, String origin) throws ApiException {
        if (!dingtalkEnable) {
            log.info("【推送钉钉用户建议数据】任务关闭");
            return;
        }
        log.info("钉钉消息发送消息内容:{}", content);
        DingTalkClient client = new DefaultDingTalkClient(dingtalkServerUrl);
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(dingtalkTitle + content);
        request.setText(text);
        if (robotSendMap.containsKey(origin)) {
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(Arrays.asList(robotSendMap.get(origin).split(",")));
            at.setIsAtAll(false);
            request.setAt(at);
            OapiRobotSendResponse response = client.execute(request);
            log.info("钉钉消息发送成功:{}", response.getBody());
        }
    }
}
