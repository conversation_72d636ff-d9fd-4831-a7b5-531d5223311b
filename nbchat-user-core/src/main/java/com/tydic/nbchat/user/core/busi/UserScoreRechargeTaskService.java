package com.tydic.nbchat.user.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.bo.constants.DicDouConstants;
import com.tydic.nbchat.user.api.bo.eums.ScoreTaskType;
import com.tydic.nbchat.user.api.bo.eums.UserVipStatusType;
import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import com.tydic.nbchat.user.api.bo.eums.VipVersionEnum;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRechargeReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.api.bo.vip.UserVipOrderContext;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.NbchatUserScoreTaskMapper;
import com.tydic.nbchat.user.mapper.NbchatUserVipLogMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask;
import com.tydic.nbchat.user.mapper.po.NbchatUserVip;
import com.tydic.nbchat.user.mapper.po.NbchatUserVipLog;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.common.nbchat.emus.VipEventType;
import com.tydic.nicc.common.nbchat.msg.RebateInviteMsgContext;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_REBATE_INVITE_TOPIC;
import static com.tydic.nicc.common.nbchat.emus.RebateInviteEnum.INVITE_PAY;

@Service
@Slf4j
public class UserScoreRechargeTaskService {

    @Value("${rebate.enable:true}")
    private boolean rebateEnable;

    @Resource
    private NbchatUserScoreTaskMapper nbchatUserScoreTaskMapper;
    @Resource
    private NbchatUserVipLogMapper nbchatUserVipLogMapper;
    @Resource
    private KKMqProducerHelper kkMqProducerHelper;

    private final ScoreRechargeService scoreRechargeService;
    private final UserVipService userVipService;
    private final UserMakeEventSender userMakeEventSender;
    private final UserSettingHelper userSettingHelper;
    private final VipSmsNoticeService vipSmsNoticeService;
    private final ScoreAccountService scoreAccountService;

    public UserScoreRechargeTaskService(ScoreRechargeService scoreRechargeService, UserVipService userVipService, UserMakeEventSender userMakeEventSender, UserSettingHelper userSettingHelper, VipSmsNoticeService vipSmsNoticeService, ScoreAccountService scoreAccountService) {
        this.scoreRechargeService = scoreRechargeService;
        this.userVipService = userVipService;
        this.userMakeEventSender = userMakeEventSender;
        this.userSettingHelper = userSettingHelper;
        this.vipSmsNoticeService = vipSmsNoticeService;
        this.scoreAccountService = scoreAccountService;
    }

    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    public void handleScoreRechargeTask(UserVipOrderContext context) {
        /**
         * 1. 判断是vip会员订单还是算力点充值订单
         * 2. 根据订单信息生成充值任务(根据充值周期计算充值时间，写入到充值任务计划表)
         */
        log.info("用户充值订单[{}]，开始处理充值任务: {}", context.getOrderNo(), context);
        if (UserVipType.SCORE_PACKAGE.getCode().equals(context.getVipType())) {
            //加油包，立即充值
            List<NbchatUserScoreTask> rechargeTasks = buildRechargeTask(context, context.getOrderTime());
            log.info("用户充值订单[{}]，创建加油包充值任务: {}", context.getOrderNo(), rechargeTasks);
            nbchatUserScoreTaskMapper.insertBatch(rechargeTasks);
            //发送加油包充值消息
            NbchatUserVip vip = new NbchatUserVip();
            vip.setVipStatus(UserVipStatusType.EXPIRED.getCode());
            vip.setVipStart(context.getOrderTime());
            vip.setVipEnd(getExpireTime(context.getOrderTime(), context.getCycle() * context.getDays()));
            vip.setTenantCode(context.getTenantCode());
            vip.setUserId(context.getUserId());
            vip.setVipType(context.getVipType());
            userMakeEventSender.sendVipChangeEvent(VipEventType.DOU_RECHARGE.getCode(), vip, context.getCycle(), context.getDays());
            context.setStartTime(context.getOrderTime());
        } else {
            //其他会员类型
            /**
             * 1. 先创建会员
             * 2. 会员存在，续费
             */
            String version = "0";
            if (context.getCycle().equals(12)) {
                version = VipVersionEnum.ANNUAL.getCode();
            }
            if (context.getCycle().equals(1) && context.getDays().equals(31)) {
                version = VipVersionEnum.MONTHLY.getCode();
            }
            NbchatUserVip userVip = this.createNewVip(context, version);
            //会员存在，续费
            if (ObjectUtils.isNotEmpty(userVip.getId())) {
                this.vipRenew(context, userVip, version);
            }
            context.setStartTime(userVip.getVipStart());
        }
        userSettingHelper.removeVipInfo(context.getTenantCode(), context.getUserId());
        log.info("用户充值订单[{}]，处理充值任务完成: {}", context.getOrderNo(), context);

        //解冻积分
        scoreAccountService.unfreezeScore(context.getTenantCode(), context.getUserId(), context.getVipType());

        //保存会员日志
        this.saveLog(context);

        // 如果要请拉新开关开启，发送邀请拉新消息
        if (rebateEnable) {
            // 发送邀请拉新消息
            RebateInviteMsgContext rebateInviteMsgContext = new RebateInviteMsgContext();
            rebateInviteMsgContext.setEventType(INVITE_PAY.getCode());
            rebateInviteMsgContext.setEventTime(new Date());
            rebateInviteMsgContext.setTenantCode(context.getTenantCode());
            rebateInviteMsgContext.setUserId(context.getUserId());
            Map<String, Object> extParams = new HashMap<>();
            extParams.put("orderNo", context.getOrderNo());
            extParams.put("vipType", context.getVipType());
            rebateInviteMsgContext.setExtParams(extParams);
            log.info("发送邀请用户消费事件消息: {}", context);
            kkMqProducerHelper.sendMsg(NBCHAT_REBATE_INVITE_TOPIC, rebateInviteMsgContext);
        }
    }

    /**
     * 创建新的会员
     */
    public NbchatUserVip createNewVip(UserVipOrderContext context, String version) {
        NbchatUserVip vipInfo = userVipService.getVipInfo(context.getTenantCode(), context.getUserId(), context.getVipType());
        if (ObjectUtils.isNotEmpty(vipInfo)) {
            log.info("用户充值订单[{}]，初次充值，会员已存在，不做处理: {}", context.getOrderNo(), vipInfo);
            return vipInfo;
        }
        //创建vip会员
        NbchatUserVip vip = new NbchatUserVip();
        vip.setTenantCode(context.getTenantCode());
        vip.setUserId(context.getUserId());
        vip.setVipType(context.getVipType());
        vip.setVipStart(context.getOrderTime());
        Date vipEnd = getExpireTime(context.getOrderTime(), context.getCycle() * context.getDays());
        vip.setVipEnd(vipEnd);
        vip.setVipStatus(UserVipStatusType.NORMAL.getCode());
        vip.setCreateTime(new Date());
        vip.setUpdateTime(new Date());
        vip.setVipDesc(UserVipType.getDescByCode(context.getVipType()));
        vip.setVipVersion(version);
        userVipService.createVip(vip);
        if (context.getScore() > 0) {
            //初次充值
            List<NbchatUserScoreTask> rechargeTasks = buildRechargeTask(context, context.getOrderTime());
            log.info("用户充值订单[{}]，初次充值，创建充值任务: {}", context.getOrderNo(), rechargeTasks);
            nbchatUserScoreTaskMapper.insertBatch(rechargeTasks);
            userMakeEventSender.sendVipChangeEvent(VipEventType.VIP_OPEN.getCode(), vip, context.getCycle(), context.getDays());
        }
        //发送短信通知
        vipSmsNoticeService.sendVipOpenSms(context.getUserId(), UserVipType.getNameByCode(context.getVipType()), vip.getVipEnd());
        return vip;
    }

    /**
     * 会员续费
     */
    public void vipRenew(UserVipOrderContext context, NbchatUserVip userVip, String version) {
        List<NbchatUserScoreTask> rechargeTasks;
        //更改会员状态
        userVip.setVipStatus(UserVipStatusType.NORMAL.getCode());
        //判断会员是否过期
        if (userVip.getVipEnd().before(new Date())) {
            //会员过期，重新设置会员时间 //如果当前是体验会员，直接做升级
            Date vipEnd = getExpireTime(context.getOrderTime(), context.getCycle() * context.getDays());
            userVipService.delayVip(userVip.getId(), context.getOrderTime(), vipEnd, context.getVipType(), version);
            userVip.setVipStart(context.getOrderTime());
            userVip.setVipEnd(vipEnd);
            rechargeTasks = buildRechargeTask(context, context.getOrderTime());
            //发送短信通知
            vipSmsNoticeService.sendVipOpenSms(context.getUserId(), UserVipType.getNameByCode(context.getVipType()), userVip.getVipEnd());
        } else {
            //没过期，直接延期
            Date vipEnd = getExpireTime(userVip.getVipEnd(), context.getCycle() * context.getDays());
            userVipService.delayVip(userVip.getId(), userVip.getVipStart(), vipEnd, context.getVipType(), version);
            //续费
            vipSmsNoticeService.sendVipRenewSms(context.getUserId(), UserVipType.getNameByCode(context.getVipType()), userVip.getVipEnd(), vipEnd);
            //生成充值任务，会员结束作为充值时间
            rechargeTasks = buildRechargeTask(context, userVip.getVipEnd());
            userVip.setVipEnd(vipEnd);
        }
        if (context.getScore() > 0) {
            log.info("用户充值订单[{}]，会员延期，创建会员充值任务: {}", context.getOrderNo(), JSONObject.toJSONString(rechargeTasks));
            nbchatUserScoreTaskMapper.insertBatch(rechargeTasks);
            userVip.setVipType(context.getVipType());
            userVip.setVipDesc(UserVipType.getDescByCode(context.getVipType()));
            userMakeEventSender.sendVipChangeEvent(VipEventType.VIP_RENEW.getCode(), userVip, context.getCycle(), context.getDays());
        }
    }

    /**
     * 1. 查询充值任务表待充值任务
     * 2. 根据任务信息进行充值
     */
    public void doTaskCheck() {
        long start = System.currentTimeMillis();
        log.info("用户积分充值任务，开始执行: {}", new Date());
        List<NbchatUserScoreTask> scoreTasks = nbchatUserScoreTaskMapper.selectWaitRechargeTask();
        for (NbchatUserScoreTask scoreTask : scoreTasks) {
            try {
                // 1. 查询充值任务表待充值任务
                // 2. 根据任务信息进行充值
                log.debug("用户积分充值任务，处理充值任务: {}", scoreTask);
                if (ScoreTaskType.RECHARGE.getCode().equals(scoreTask.getTaskType())) {
                    // 充值
                    UserBalanceRechargeReqBO recharge = new UserBalanceRechargeReqBO();
                    recharge.setUserId(scoreTask.getUserId());
                    recharge.setScoreType(scoreTask.getScoreType());
                    recharge.setScore(scoreTask.getScore());
                    recharge.setTenantCode(scoreTask.getTenantCode());
                    recharge.setRemark(scoreTask.getTaskDesc());
                    recharge.setExpireTime(scoreTask.getExpireTime());
                    recharge.setOrderNo(scoreTask.getOrderNo());
                    Rsp<UserTradeResult> resultRsp = scoreRechargeService.recharge(recharge);
                    if (resultRsp.isSuccess()) {
                        vipSmsNoticeService.sendDouRechargeSms(scoreTask.getUserId(), scoreTask.getScore());
                        // 更新任务状态
                        updateTaskStatus(scoreTask.getTaskId(), EntityValidType.NORMAL.getCode());
                        //发送算力点充值事件
                        userMakeEventSender.sendScoreChangeEvent(resultRsp.getData(), resultRsp.getData().getBalance().getScore());
                    } else {
                        log.error("用户积分充值任务，充值失败: {}, {}", scoreTask, resultRsp);
                        updateTaskStatus(scoreTask.getTaskId(), "2");
                    }
                } else if (ScoreTaskType.REFUND.getCode().equals(scoreTask.getTaskType())) {
                    // 退款 TODO 暂不支持
                } else {
                    log.error("用户积分充值任务，任务类型错误: {}", scoreTask);
                }
            } catch (Exception e) {
                log.error("用户积分充值任务，处理异常: {}", scoreTask, e);
                updateTaskStatus(scoreTask.getTaskId(), "2");
            }
        }
        long end = System.currentTimeMillis();
        log.info("用户积分充值任务，执行完成: {},{} ms", scoreTasks.size(), (end - start));
    }

    private void updateTaskStatus(Long taskId, String state) {
        NbchatUserScoreTask updateTask = new NbchatUserScoreTask();
        updateTask.setTaskId(taskId);
        updateTask.setTaskStatus(state);
        updateTask.setUpdateTime(new Date());
        nbchatUserScoreTaskMapper.updateByPrimaryKeySelective(updateTask);
    }

    private List<NbchatUserScoreTask> buildRechargeTask(UserVipOrderContext context, Date startTime) {
        Integer cycle = context.getCycle();
        List<NbchatUserScoreTask> list = Lists.newArrayList();
        Date startDate = DateUtils.truncate(startTime, Calendar.DAY_OF_MONTH);
        for (int i = 0; i < cycle; i++) {
            Date execTime = DateUtils.addMonths(startDate, i);
            NbchatUserScoreTask scoreTask = new NbchatUserScoreTask();
            scoreTask.setTenantCode(context.getTenantCode());
            scoreTask.setUserId(context.getUserId());
            scoreTask.setScore(context.getScore());
            scoreTask.setOrderNo(context.getOrderNo());
            scoreTask.setTaskType(ScoreTaskType.RECHARGE.getCode());
            scoreTask.setScoreType(context.getVipType());
            if (UserVipType.SCORE_PACKAGE.getCode().equals(context.getVipType())) {
                scoreTask.setTaskDesc(DicDouConstants.RECHARGE_DOU_REMARK);
                scoreTask.setExpireTime(getExpireTime(startTime, DicDouConstants.RECHARGE_DOU_EXPIRE_DAY));
            } else {
                scoreTask.setTaskDesc(UserVipType.getNameByCode(context.getVipType()) + "—每月赠送算力点");
                scoreTask.setExpireTime(getExpireTime(startTime, DicDouConstants.VIP_DOU_EXPIRE_DAY));
            }
            scoreTask.setExecTime(execTime);
            scoreTask.setTaskStatus(EntityValidType.DELETE.getCode());
            scoreTask.setCreateTime(new Date());
            list.add(scoreTask);
            //计算下次充值的时间
            startTime = DateTimeUtil.DateAdd(startTime, 6, context.getDays());
        }
        return list;
    }

    private static Date getExpireTime(Date startTime, Integer days) {
        Date date = DateTimeUtil.DateAdd(startTime, 6, days);
        return DateTimeUtil.createTime(date, 23, 59, 58);
    }

    public void saveLog(UserVipOrderContext context) {
        log.info("用户充值订单，保存会员日志: {}", context);
        try {
            NbchatUserVipLog po = new NbchatUserVipLog();
            po.setUserId(context.getUserId());
            po.setTenantCode(context.getTenantCode());
            po.setVipType(context.getVipType());
            po.setValidDays(context.getCycle() * context.getDays());
            po.setAmount(context.getPayPrice());
            po.setStartTime(context.getStartTime());
            po.setEndTime(getExpireTime(po.getStartTime(), context.getCycle() * context.getDays()));
            po.setCreateTime(new Date());
            po.setTotalDou(context.getScore() * context.getCycle());
            po.setOrderNo(context.getOrderNo());

            nbchatUserVipLogMapper.insertSelective(po);
        } catch (Exception e) {
            log.error("用户充值订单，保存会员日志异常: {}", context, e);
        }
    }

}
