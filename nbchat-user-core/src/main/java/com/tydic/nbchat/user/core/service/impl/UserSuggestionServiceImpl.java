package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.UserSuggestionService;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.suggestion.SuggestionSaveReqBO;
import com.tydic.nbchat.user.api.bo.suggestion.UserVideoIssuesReqBo;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserSuggestionMapper;
import com.tydic.nbchat.user.mapper.po.UserSuggestion;
import com.tydic.nbchat.user.core.utils.DingtalkUtil;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
@Service
public class UserSuggestionServiceImpl implements UserSuggestionService {

    @Resource
    private UserSuggestionMapper userSuggestionMapper;

    private final UserSettingHelper userSettingHelper;
    @Resource
    private UserBaseInfoApi userBaseInfoApi;

    public UserSuggestionServiceImpl(UserSettingHelper userSettingHelper) {
        this.userSettingHelper = userSettingHelper;
    }

    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @MethodParamVerifyEnable
    @Override
    public Rsp saveSuggestion(SuggestionSaveReqBO saveReqBO) {
        log.info("保存用户建议:{}",saveReqBO);
        if(StringUtils.isNotBlank(saveReqBO.getContent()) &&
                saveReqBO.getContent().length() > 500){
            return BaseRspUtils.createErrorRsp("内容不得超过500字符!");
        }
        if (StringUtils.isNotBlank(saveReqBO.getUserId())) {
            Rsp<UserBaseInfo> userRsp = userBaseInfoApi.getByUserId(saveReqBO.getTenantCode(),saveReqBO.getUserId());
            if (userRsp.isSuccess()) {
                UserBaseInfo userInfo = userRsp.getData();
                if (userInfo != null) {
                    if (StringUtils.isBlank(saveReqBO.getUsername())) {
                        saveReqBO.setUsername(userInfo.getRealName());
                    }
                    if (StringUtils.isBlank(saveReqBO.getPhone())) {
                        saveReqBO.setPhone(userInfo.getPhone());
                    }
                }
            }
        }
        UserSuggestion suggestion = new UserSuggestion();
        BeanUtils.copyProperties(saveReqBO,suggestion);
        suggestion.setCreateTime(new Date());
        if(!CollectionUtils.isEmpty(saveReqBO.getAttachmentList())){
            String attachment = JSONArray.toJSONString(saveReqBO.getAttachmentList());
            suggestion.setAttachment(attachment);
        }else{
            suggestion.setAttachment("[]");
        }
        // 新增逻辑：当 pageSource=2 时，判断 busiId 是否存在以决定是新增还是更新
        if ("2".equals(saveReqBO.getPageSource()) && StringUtils.isNotBlank(saveReqBO.getBusiId())) {
            UserSuggestion existing = userSuggestionMapper.selectByBusiId(saveReqBO.getBusiId());
            if (existing != null) {
                // 更新操作
                suggestion.setSuggestId(existing.getSuggestId());
                userSuggestionMapper.updateSuggestion(suggestion);
            } else {
                // 新增操作
                userSuggestionMapper.insertSelective(suggestion);
            }
        } else {
               userSuggestionMapper.insertSelective(suggestion);
        }
        //推送钉钉消息
        sendDingNotification(saveReqBO);
        return BaseRspUtils.createSuccessRsp("操作成功!");
    }

/* 钉钉推送消息格式：
    【测试/生产环境】
    用户意见反馈数据
    类型：
    产品模块：
    反馈内容：
    上传截图或视频：
    姓名：
    联系方式：*/
private void sendDingNotification(SuggestionSaveReqBO reqBO) {
    try {
        StringBuilder message = new StringBuilder();
        // 动态设置标题
        String title = "";
        if ("2".equals(reqBO.getPageSource())) {
            title = "【问题上报】";
        } else {
            title = "【用户意见反馈数据】"; // 或者保留原样
        }
        message.append(title).append("\n")
                .append("类型：").append(reqBO.getIssueTypeName()).append("\n")
                .append("产品模块：").append(reqBO.getProductModuleName()).append("\n")
                .append("问题上报内容：").append(reqBO.getContent()).append("\n");
        StringBuilder accessUrls = new StringBuilder();
        if (!CollectionUtils.isEmpty(reqBO.getAttachmentList())) {
            reqBO.getAttachmentList().forEach(attachment -> accessUrls.append(attachment.getAccessUrl()).append("\n"));
        } else {
            accessUrls.append("\n");
        }
        message.append("上传截图或视频：").append(accessUrls)
                .append("姓名：").append(reqBO.getUsername()).append("\n")
                .append("联系方式：").append(reqBO.getPhone());
        if ("2".equals(reqBO.getPageSource())) {
            message.append("\n异常时间点：")
                    .append(reqBO.getPartStartTime())
                    .append(" - ")
                    .append(reqBO.getPartEndTime());
        }
        NbchatUserInfo userInfo = userSettingHelper.getUserInfo(reqBO.getUserId());
        if (userInfo != null && StringUtils.isNotEmpty(userInfo.getPhone())) {
            message.append("\n平台注册手机号：").append(userInfo.getPhone());
        }
        // 动态选择 origin 类型
        String origin = "tdh-portal"; // 默认值
        if ("2".equals(reqBO.getPageSource())) {
            origin = "tdh-admin";
        }
        DingtalkUtil.sendMessageSuggestion(message.toString(), origin);
    } catch (Exception e) {
        log.error("钉钉推送失败，手机号: {}", reqBO.getPhone(), e);
        throw new RuntimeException("消息推送失败", e);
    }
}

/**
 * 根据业务ID查询建议
 */
    @Override
    public Rsp<?> getSuggestionByBusiId(UserVideoIssuesReqBo reqBO) {
        log.info("根据业务ID查询建议，busiId: {}", reqBO.getBusiId());
        if (StringUtils.isBlank(reqBO.getBusiId())) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        UserSuggestion  userSuggestion =  new UserSuggestion();
        BeanUtils.copyProperties(reqBO,userSuggestion);
        UserSuggestion rspBO = userSuggestionMapper.selectByBusiId(reqBO.getBusiId());
        return BaseRspUtils.createSuccessRsp(rspBO);
    }
}
