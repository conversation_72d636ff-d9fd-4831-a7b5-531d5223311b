package com.tydic.nbchat.user.core.web;

import com.tydic.nbchat.user.api.UserSuggestionService;
import com.tydic.nbchat.user.api.UserVideoIssuesApi;
import com.tydic.nbchat.user.api.bo.suggestion.*;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserSuggestController {
    private final UserSuggestionService userSuggestionService;
    private final UserVideoIssuesApi  userVideoIssuesApi;

    public UserSuggestController(UserSuggestionService userSuggestionService, UserVideoIssuesApi userVideoIssuesApi) {
        this.userSuggestionService = userSuggestionService;
        this.userVideoIssuesApi = userVideoIssuesApi;
    }
    @PostMapping("/suggest/save")
    public Rsp saveSuggestion(@RequestBody SuggestionSaveReqBO reqBO){
        return userSuggestionService.saveSuggestion(reqBO);
    }

    /**
     * 运营平台用户视频问题反馈列表查询
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @PostMapping("/suggest/admin_list")
    public RspList<UserVideoIssuesBO> getVideoIssuesList(@RequestBody UserVideoIssuesReqBo queryReqBO){
        return userVideoIssuesApi.getVideoIssuesList(queryReqBO);
    }
    /**
     * 运营平台用户意见反馈列表查询
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @PostMapping("/suggest/suggestion_list")
    public RspList<UserVideoIssuesBO> getSuggestionList(@RequestBody UserVideoIssuesReqBo queryReqBO){
        return userVideoIssuesApi.getSuggestionList(queryReqBO);
    }
    /**
     * 用户反馈视频算力点消耗查询
     */
    @PostMapping("/suggest/video_cost")
    public RspList<UserVideoIssuesBO> selectVideoCostByBizId(@RequestBody UserVideoIssuesReqBo queryReqBO){
        return userVideoIssuesApi.selectVideoCostByBizId(queryReqBO);
    }
    /**
     * 用户反馈视频算力点退款
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @PostMapping("/suggest/video_refund")
    public Rsp adminAuditVideoIssues(@RequestBody UserVideoIssuesReqBo reqBO){
        return userVideoIssuesApi.adminAuditVideoIssues(reqBO);
    }
    /**
     * 根据busiId查询视频问题上报
     */
    @PostMapping("/suggest/video_issues")
    public Rsp<?> getSuggestionByBusiId(@RequestBody UserVideoIssuesReqBo reqBO){
        return userSuggestionService.getSuggestionByBusiId(reqBO);
    }
    /**
     * 用户反馈视频算力点赠送
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    @PostMapping("/suggest/video_give")
    public Rsp adminGiveVideoIssues(@RequestBody UserVideoIssuesReqBo reqBO){
        return userVideoIssuesApi.adminGiveVideoIssues(reqBO);
    }
    /**
     * 问题上报、意见反馈审核状态数据近三十天统计
     */
    @PostMapping("/suggest/stats")
    public Rsp<UserSuggestionRspBO> selectSuggestionStats(@RequestBody UserVideoIssuesReqBo reqBO){
        return userVideoIssuesApi.selectSuggestionStats(reqBO);
    }
}
