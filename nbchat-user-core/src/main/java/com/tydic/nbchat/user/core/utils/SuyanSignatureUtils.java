package com.tydic.nbchat.user.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;

import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 苏研云接口请求签名工具类
 * 移动云API服务会对每个访问的请求进行身份验证
 */
@Slf4j
public class SuyanSignatureUtils {

    private SimpleDateFormat sdf = new SimpleDateFormat(TIME_STAMP_FORMAT);

    public static final String ACCESS_KEY = "AccessKey";

    public static final String TIME_STAMP = "Timestamp";

    public static final String SIGNATURE = "Signature";

    public static final String SIGNATURE_METHOD = "SignatureMethod";

    public static final String SIGNATURE_VERSION = "SignatureVersion";

    public static final String SIGNATURE_NONCE = "SignatureNonce";

    public static final String SIGNATURE_VERSION_VALUE = "V2.0";

    public static final String SIGNATURE_METHOD_VALUE = "HmacSHA1";

    public static final String TIME_STAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static final String URL_ENCODER_FORMAT = "%s=%s";

    public static final String ENCODING = "utf-8";

    public static final String STRING_SEPARATOR = "\n";

    public static final String PARAMETER_SEPARATOR = "&";

    public static final String SIGNING_STRING = "BC_SIGNATURE&";

    public String accessKey;

    public String secretKey;

    /**
     * 构造函数
     * @param accessKey
     * @param secretKey
     */
    public SuyanSignatureUtils(String accessKey, String secretKey) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
    }

    public SuyanSignatureUtils() {
    }

    //参数排序及整理
    public String doSignature(String servletPath, String method, Map<String, Object> query) {
        try {
            servletPath = java.net.URLDecoder.decode(servletPath, ENCODING);
            query.put(ACCESS_KEY, accessKey);
            query.put(TIME_STAMP, sdf.format(new Date()));
            query.put(SIGNATURE_NONCE, UUID.randomUUID().toString().replace("-", ""));
            query.put(SIGNATURE_VERSION, SIGNATURE_VERSION_VALUE);
            query.put(SIGNATURE_METHOD, SIGNATURE_METHOD_VALUE);
            ArrayList<String> parameterList = new ArrayList<>(query.keySet());
            Collections.sort(parameterList);
            List<String> list = new ArrayList<>(query.size());
            for (String name : parameterList) {
                if (!SIGNATURE.equalsIgnoreCase(name)) {
                    String value;
                    if (query.get(name) instanceof Boolean) {
                        value = Boolean.getBoolean(name) ? "true" : "false";
                    } else {
                        value = query.get(name).toString();
                    }
                    list.add(String.format(URL_ENCODER_FORMAT,
                            percentEncode(name), percentEncode(value)));
                }

            }

            String canonicalizedQueryString = String.join(PARAMETER_SEPARATOR, list);
            String encryptedCanonicalizedQueryStr =
                    encode(canonicalizedQueryString);
            StringBuilder sb = new StringBuilder();
            sb.append(method.toUpperCase());
            sb.append(STRING_SEPARATOR);
            sb.append(percentEncode(servletPath));
            sb.append(STRING_SEPARATOR);
            sb.append(encryptedCanonicalizedQueryStr);
            String signature = sign(SIGNING_STRING + secretKey, sb.toString());
            if (Objects.isNull(signature) || signature.length() == 0) {
                return null;
            }
            return servletPath + "?" + canonicalizedQueryString + PARAMETER_SEPARATOR + String.format(URL_ENCODER_FORMAT, SIGNATURE, percentEncode(signature));
        } catch (Exception e) {
            return null;
        }

    }


    //参数编码
    public static String percentEncode(String value) throws
            UnsupportedEncodingException {
        return value != null ? URLEncoder.encode(value, ENCODING).replace("+",
                "%20").replace("*", "%2A").replace("%7E", "~") : null;
    }


    //计算签名
    public static String sign(String secretKey, String data) {
        try {
            Mac mac = Mac.getInstance(SIGNATURE_METHOD_VALUE);
            byte[] secretKeyByte = secretKey.getBytes(ENCODING);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyByte, SIGNATURE_METHOD_VALUE);
            mac.init(secretKeySpec);
            return new String(encodeHex(mac.doFinal(data.getBytes(ENCODING))));
        } catch (NoSuchAlgorithmException | InvalidKeyException |
                 UnsupportedEncodingException e) {
            return null;
        }

    }


    public static String encode(String data) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(data.getBytes(ENCODING));
        return new String(encodeHex(hash));
    }


    protected static char[] encodeHex(final byte[] data) {
        char[] toDigits =
                {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b',
                        'c', 'd', 'e', 'f'};
        final int l = data.length;
        final char[] out = new char[l << 1];
        // two characters form the hex value.
        for (int i = 0, j = 0; i < l; i++) {
            out[j++] = toDigits[(0xF0 & data[i]) >>> 4];
            out[j++] = toDigits[0x0F & data[i]];
        }
        return out;

    }



/*    public static void main(String[] args) {

        *//*
         * 输入免登码，requestId 和sessionId
         *//*
        String authCode = "eyJhbGciOiJIUzI1NiJ9.eyJhdXRoU2Vzc2lvbklkIjoiOTQ2ZjM3MWNmNDJhNDUxMTg4MzUwOWM0M2E2NDM5NGYiLCJhcHBJZCI6IjljOGZlN2FkMmNkYjQxNzRhMmEzOWYyNDUwZGMzZjRhIiwiaWQiOiI3MWMzMjEwODVjNTU0YjQwYjQ5MmVlOTBhNTRlOTkwNCJ9.1fpwR9eSWrl5ZpmX7EzFRU-RQljUMBsY3ZV1A5GXG68";
        String requestId = "reqId-reqId-8b383615644941f5aa189c6a89";
        String sessionId = "946f371cf42a4511883509c43a64394f";

        SuyanSignatureUtils e = new SuyanSignatureUtils();
        //您的accessKey
        e.accessKey = "P6dYBjq9WLsQ0ATJy3ohyT0f0TSt";
        //您的secretKey
        e.secretKey = "8TGgznaCvxejFKxRANAJ1nIBC3dnmp";

        String uri = e.doSignature("/api/cem/cem_sso/clientAuth/sideAppCall/v1/validateAuthCode", "POST", new HashMap<>());

        String hp = "https://ecloud.10086.cn";
        Map<String, String> headers = new HashMap<>();
        headers.put("csa-request-id", requestId);
        headers.put("csa-auth-session-id", sessionId);
        String jsonBody = "{\"appId\": \"9c8fe7ad2cdb4174a2a39f2450dc3f4a\", \"appSecret\": \"73cb9c7102d04c9e81dfb9c6ad505afe\", " +
                "\"authCode\": \"" + authCode +
                "\"}";
        String result = HttpClientHelper.doPost(hp + uri, headers, jsonBody);
        log.info("检查-结果: {}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (!"200".equals(jsonObject.getString("errorCode"))) {
            log.info("登录失败：" + jsonObject.getString("errorMessage"));
        }

        String accessToken = jsonObject.getJSONObject("body").getString("accessToken");
        System.out.println("accessToken:" + accessToken);
        headers.put("csa-access-token", accessToken);
        String urUser = e.doSignature("/api/cem/cem_sso/clientAuth/sideAppCall/v1/queryEcloudComputerUser", "POST", new HashMap<>());
        String jsonUser = "{}";
        String resultUser = HttpClientHelper.doPost(hp + urUser, headers, jsonUser);
        log.info("用户-用户结果: {}|{}", accessToken, resultUser);
    }
    */
}