package com.tydic.nbchat.train.api.bo.eums;

/**
 * 数字人：虚拟人形象类型
 */
public enum TdhHumanType {
    /**
     * 卡通数字人
     */
    GIF("0","2d_gif"),
    /**
     * 定制视频数字人
     */
    CUSTOM("1","2.5d_mtk"),
    /**
     * 定制照片数字人
     */
    CUSTOM_PHOTO("2","2d_mtk"),
    /**
     * 训练数字人
     */
    RAIN("3","2.5d_wp"),

    CUSTOM2("4","2.5d_mtk2")
    ;

    private String code;
    private String name;
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    TdhHumanType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static TdhHumanType getByCode(String code) {
        for (TdhHumanType type : TdhHumanType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    public static TdhHumanType getByName(String name) {
        for (TdhHumanType type : TdhHumanType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

}
