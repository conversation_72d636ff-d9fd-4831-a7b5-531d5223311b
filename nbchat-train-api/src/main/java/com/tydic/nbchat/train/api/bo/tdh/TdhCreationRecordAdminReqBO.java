package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nbchat.train.api.bo.task.VipItemReq;
import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 数字人-用户创作记录(TdhCreationRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:53
 */
@Data
public class TdhCreationRecordAdminReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 453296879570050790L;
    /**
     * 创作id
     */
    private String creationId;
    /**
     * 租户编码
     */
    private String targetTenant;
    /**
     * 用户id
     */
    private String targetUid;
    /**
     * 关键字搜索
     */
    private String keyword;

    /**
     * 片段数
     */
    private Integer partMinCount;
    private Integer partMaxCount;

    /**
     * 创建时间
     */
    private Date createTimeStart;
    private Date createTimeEnd;
    /**
     * 更新时间
     */
    private Date updateTimeStart;
    private Date updateTimeEnd;

    /**
     * 创作状态
     */
    private String creationState; //2自动创建未生成草稿内容

    /**
     * 1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 内容分类：1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    private String isShare;
    private Integer isPay;
    // 用户类型
    private String userType;
    private String phone;

    private List<VipItemReq> vipTypes;

    //行业
    private String industry;


}

