package com.tydic.nbchat.train.api.bo.video;

import com.tydic.nbchat.train.api.bo.task.VipItemReq;
import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class NbchatVideoQueryReqBO extends BasePageInfo implements Serializable {

    // 任务ID、作品ID、目标用户ID、名称内容
    private String taskId;
    private String creationId;
    private String targetUserId;
    private String keyword;
    // VIP类型列表 非会员、高级会员-月、专业会员-月、专业会员max-月、高级会员-年、专业会员-年、专业会员max-年
    private List<VipItemReq> vipTypes;
    // 视频标签： 双语字幕、智能进度条、片头片尾、文字模版、定制声音、定制形象、板书标记、字幕标记、上传视频、数字人镜头
    private Integer subtitleBilingual;
    private Integer videoBar;
    private Integer opId;
    private Integer edId;
    private Integer pagMap;
    private Integer customVoice;
    private Integer customTdh;
    private Integer banshuMark;
    private Integer subtitleMark;
    private Integer pipUpload;
    private Integer tdhCrop;
    //行业
    private String industry;



    private Date createTimeBegin;
    private Date createTimeEnd;
    /**
     * 租户ID
     */
    private String targetTenantCode;
    /**
     * 视频类型;1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 视频时长
     */
    private Integer minVideoDuration;
    /**
     * 视频时长
     */
    private Integer maxVideoDuration;
    /**
     * 制作时间
     */
    private Date startTime;
    /**
     * 制作时间
     */
    private Date endTime;
    /**
     * 内容分类;1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    /**
     * 付费状态
     */
    private String isPay;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 视频状态
     */
    private List<String> taskStateList;
    // 闲时队列传入 1
    private String idleQueue;
    // 是否删除
    private String isValid;
    /**
     * 是否进行分页;默认进行分页
     */
    private Boolean isPaged = true;

}
