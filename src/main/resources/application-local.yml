# 加上下文路径
server:
  port: 8700

spring:
  redis:
    host: ************** #************** #127.0.0.1
    port: 6389 #16379
    password: Redis235 #Redis235 #Mtj1qJSwLF
    timeout: 1000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-wait: -1
        max-active: 1000
        max-idle: 10
        min-idle: 5
    database: 5

proxy:
  # 是否输出日志
  printLog: true
  exclude-paths:
    - /actuator/health
  servlets:
    - name: proxySession
      mapping: /api/session/*
      proxyUrl: http://dev.nbchat.com:8700/api/session
      proxyDesc: session
      http-config:
        connection-request-timeout: 1000
        read-timeout: 1000
    - name: proxyRobot
      mapping: /api/robot/*
      proxyUrl: http://dev.nbchat.com:8700/api/robot
      proxyDesc: robot
    - name: proxyUser
      mapping: /api/user/*
      proxyUrl: http://localhost:8703/user
      proxyDesc: user
      http-config:
        connection-request-timeout: 1000
        read-timeout: 1000
        preserve-cookies: true
    - name: proxyAdmin
      mapping: /api/admin/*
      proxyUrl: http://dev.nbchat.com:8700/api/admin
      proxyDesc: admin
      http-config:
        connection-request-timeout: 1000
        read-timeout: 1000
    - name: proxyTrain
      mapping: /api/train/*
      proxyUrl: http://dev.nbchat.com:8700/api/train
      proxyDesc: embeddings
    - name: proxyIndex
      mapping: /*
      proxyUrl: http://localhost:8087
      proxyDesc: 代理默认首页
    - name: proxyBaiduAuth
      mapping: /baidu_verify_codeva-kjCMjxoHhh.html
      proxyUrl: https://chatfiles.tydiczt.com/files/baidu_verify_codeva-kjCMjxoHhh.html
      proxyDesc: 百度认证

# dubbo 服务配置
dubbo:
  application:
    name: nbchat-gateway
  registry:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    # 可以通过url ?namespace=nicc-env-dev&group=NICC的形式配置，也可以通过这种parameters配置
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  provider:
    threads: 300
    threadpool: cached
    loadbalance: roundrobin
    version: 1.0
    group: NICC
  protocol:
    name: dubbo
    port: -1
  # 配置中心和元数据
  config-center:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  metadata-report:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  consumer:
    check: false
    version: 1.0
    group: NICC

nicc-dc-config:
  dubbo-provider:
    version: 1.0
    group: NICC

# 数据中心相关组件配置
nicc-plugin:
  redis:
    enable: true # 是否开启redisHelper，开启后必须配置spring.redis，同时直接用RedisHelper
  oss:
    enable: false
  rest:
    enable: true # 开启restTemplate 组件
    connect-timeout: 5000
    read-timeout: 5000
    print-log: true
  kkmq:
    mq-type: rocketmq
    log-level: warn
    name-server: **************:9878 # rocketmq服务地址
    producer:
      group: nbchat_gateway # 自定义的组名称

nbchat-gateway:
  config:
    auth-enable: true
    white-list: /assets/*,/api/admin/talk/msg,/api/user/auth/login_check,/api/user/auth/suyan/login_check,/api/user/wx/qrcode,/api/user/wx/callback,/api/pay/trade/wechat/callback,/smart-solution/pages/**,/kb-portal/**,/op-admin/**,/portal/**,/aigc-demo-portal/**,/tdh-portal/**,/tmo-admin/**,/unicom-tdh-portal/**,/unicom-h5/**,/unicom-pc/**,/favicon.ico,/actuator/health,/api/tdh/file/**,/api/nls/files/**,/files/**,/chat/**,/chat-pc/**,/chatv1/**,/api/auth/logout,/api/auth/verify/code,/api/user/captcha/get,/api/user/sms/send,/api/auth/login,/api/auth/user/login,/api/user/regist,/api/admin/token/refresh,/api/train/tdh/template/list
    auth-properties:
      wchat-login: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxc590c4aff170f8d5&redirect_uri=https://chat.tydiczt.com/api/wchat/login&response_type=code&scope=snsapi_base#wechat_redirect
      main-page: http://dev.nbchat.com:8700/chat/
      login-page: http://dev.nbchat.com:8700/chat/login/
      invite-page: http://dev.nbchat.com:8700/chat/invite/
      mock-user: 1
      mock-tenant: 1000
    token-users: {"mE3gE1bZ6lgW6pA3pB4bwH7aS5pG5d":"100000000000001"}
    encrypt-apis: /api/user/sms/send