package com.tydic.nbchat.pay.core.busi.custom;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.SysTenantManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nbchat.pay.api.bo.CustomizeReqBO;
import com.tydic.nbchat.pay.api.bo.emus.PayChannelEmus;
import com.tydic.nbchat.pay.core.enums.OrderStatusEnum;
import com.tydic.nbchat.pay.core.enums.OrderTypeEnums;
import com.tydic.nbchat.pay.core.enums.PayTypeEnum;
import com.tydic.nbchat.pay.mapper.NameMapper;
import com.tydic.nbchat.pay.mapper.PayTradeRecordMapper;
import com.tydic.nbchat.pay.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.pay.mapper.po.PayTradeRecord;
import com.tydic.nbchat.pay.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.api.CustomizeOrderApi;
import com.tydic.nbchat.user.api.bo.eums.TradePayType;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceDeductReqBO;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class PayCustomOrderService {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private CustomizeOrderApi customizeOrderApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBalanceApi tradeBalanceApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private SysTenantManagementApi tenantManagementApi;

    @Resource
    TdhCustomizeRecordMapper tdhCustomizeRecordMapper;
    @Resource
    PayTradeRecordMapper payTradeRecordMapper;
    @Resource
    NameMapper nameMapper;

    /**
     * 1、获取订单修改金额为迪豆数量
     * 2、调用用户模块扣除迪豆
     * 3、更新订单状态
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Rsp payForDou(CustomizeReqBO request) {
        log.info("迪豆支付-定制商品:{}", request);
        if ("00000000".equals(request.getTenantCode())) {
            log.warn("个人租户不支持迪豆支付定制");
            return BaseRspUtils.createErrorRsp("个人租户请使用其他支付方式");
        }
        String orderNo = request.getOrderNo();

        TdhCustomizeRecord record = tdhCustomizeRecordMapper.queryByOrderId(orderNo);
        if (ObjectUtils.isEmpty(record)) {
            log.warn("订单不存在:{}", orderNo);
            return BaseRspUtils.createErrorRsp("订单不存在");
        }

        Integer score = this.getDouByType(request.getTenantCode(), record.getCustomizeType());
        String bizName = StringUtils.isEmpty(record.getTdhName()) ? record.getVoiceName() : record.getTdhName();

        Rsp deductRsp = this.deduct(request, score, bizName);
        if (!deductRsp.isSuccess()) {
            log.error("当前算力点余额不足:{}", deductRsp);
            return BaseRspUtils.createErrorRsp("当前算力点余额不足");
        }

        this.saveTrade(request, score);

        this.payFinal(orderNo, score);

        return BaseRspUtils.createSuccessRsp(null);
    }

    private Integer getDouByType(String tenantCode, String type) {
        SysTenantManagementReqBO request = SysTenantManagementReqBO.builder().targetTenantCode(tenantCode).build();
        Rsp rsp = tenantManagementApi.queryCustomConfig(request);
        if (!rsp.isSuccess()) {
            log.error("获取租户定制配置失败:{}", rsp);
            return null;
        }
        JSONObject data = (JSONObject) rsp.getData();
        Integer score = data.getJSONObject("custom").getJSONObject(type).getInteger("score");
        if (score == null) {
            score = 20000; // 需求默认20000
        }
        return score;
    }

    // 记录流水
    private void saveTrade(CustomizeReqBO request, Integer score) {
        PayTradeRecord trade = PayTradeRecord.builder()
                .tradeNo(IdWorker.nextAutoIdStr())
                .tenantCode(request.getTenantCode())
                .userId(request.getUserId())
                .payNo(IdWorker.nextAutoIdStr())
                .channel(PayChannelEmus.DOU.getCode())
                .orderNo(request.getOrderNo())
                .payPrice(score)
                .payStatus("SUCCESS")
                .payDesc("迪豆支付成功")
                .payTime(new Date())
                .payType(PayTypeEnum.DOU.name())
                .busiType(OrderTypeEnums.customize.getCode())
                .build();
        trade.setPayTime(new Date());
        trade.setUserId(request.getUserId());
        trade.setTenantCode(request.getTenantCode());
        String userName = nameMapper.queryUserName(request.getUserId(), request.getTenantCode());
        trade.setUpdateBy(userName);
        payTradeRecordMapper.insertSelective(trade);
    }

    // 调用user 模块扣豆
    private Rsp deduct(CustomizeReqBO request, Integer score, String bizName) {
        UserBalanceDeductReqBO reqBO = new UserBalanceDeductReqBO();
        reqBO.setTenantCode(request.getTenantCode());
        reqBO.setUserId(request.getUserId());
        reqBO.setPayType(TradePayType.ENTERPRISE.getCode());
        reqBO.setAmount(1);
        reqBO.setBizId(request.getOrderNo());
        reqBO.setBizName(bizName);
        reqBO.setRemark("企业支付");
        reqBO.setBizCode("custom_pay_for_dou");
        reqBO.setScore(score);

        return tradeBalanceApi.deduct(reqBO);
    }

    // 更新订单
    private void payFinal(String orderNo, Integer score) {
        TdhCustomizeRecord po = new TdhCustomizeRecord();
        po.setOrderNo(orderNo);
        po.setPayPrice(score);
        po.setOrderStatus(OrderStatusEnum.PAY_SUCCESS.getCode());
        po.setStartTime(new Date());
        po.setEndTime(DateTimeUtil.DateAddYear(1));
        tdhCustomizeRecordMapper.update(po);
    }

}
