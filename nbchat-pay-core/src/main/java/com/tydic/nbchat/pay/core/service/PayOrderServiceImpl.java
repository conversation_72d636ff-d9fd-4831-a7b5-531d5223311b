package com.tydic.nbchat.pay.core.service;

import com.tydic.nbchat.pay.api.PayOpDataCountApi;
import com.tydic.nbchat.pay.api.PayOrderApi;
import com.tydic.nbchat.pay.api.bo.*;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountReqBO;
import com.tydic.nbchat.pay.api.bo.emus.InvoiceStatusType;
import com.tydic.nbchat.pay.api.bo.emus.PayBusiType;
import com.tydic.nbchat.pay.core.busi.order.PayOrderService;
import com.tydic.nbchat.pay.core.busi.order.RefundOrderService;
import com.tydic.nbchat.pay.core.enums.OrderStatusEnum;
import com.tydic.nbchat.pay.core.enums.OrderTypeEnums;
import com.tydic.nbchat.pay.core.helper.AttachDataHelper;
import com.tydic.nbchat.pay.core.helper.pay.PayHelperFactory;
import com.tydic.nbchat.pay.core.helper.pay.api.bo.ProduceOrderRequest;
import com.tydic.nbchat.pay.core.service.notify.AliNotifyServiceImpl;
import com.tydic.nbchat.pay.core.utils.RedisKeyUtils;
import com.tydic.nbchat.pay.mapper.PayInvoiceRecordMapper;
import com.tydic.nbchat.pay.mapper.po.PayGoodsSku;
import com.tydic.nbchat.pay.mapper.po.PayTradeRecord;
import com.tydic.nbchat.rebate.api.RebatePayApi;
import com.tydic.nbchat.rebate.api.bo.UserInfoReqBO;
import com.tydic.nbchat.rebate.api.bo.coupon.UserCouponResBO;
import com.tydic.nbchat.rebate.api.bo.pay.BalanceResBO;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PayOrderServiceImpl implements PayOrderApi {
    private final RedisHelper redisHelper;
    private final PayOrderService payOrderService;
    private final PayHelperFactory payHelperFactory;
    private final PayOpDataCountApi payOpDataCountApi;
    private final AliNotifyServiceImpl aliNotifyService;
    private final PromotionService promotionService;
    private final RefundOrderService refundOrderService;

    @Resource
    PayInvoiceRecordMapper payInvoiceRecordMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000
    )
    RebatePayApi rebatePayApi;

    public PayOrderServiceImpl(RedisHelper redisHelper,
                               PayOrderService payOrderService,
                               PayHelperFactory payHelperFactory,
                               PayOpDataCountApi payOpDataCountApi,
                               AliNotifyServiceImpl aliNotifyService,
                               PromotionService promotionService, RefundOrderService refundOrderService) {
        this.redisHelper = redisHelper;
        this.payOrderService = payOrderService;
        this.payHelperFactory = payHelperFactory;
        this.payOpDataCountApi = payOpDataCountApi;
        this.aliNotifyService = aliNotifyService;
        this.promotionService = promotionService;
        this.refundOrderService = refundOrderService;
    }

    /**
     * 附加数据 attach
     * 格式 orderType:1,promotionKey:123456
     */
    @Override
    public Rsp createOrder(PayCreateOrderReqBO request) {
        log.info("创建订单：{}", request);

        // 校验 SKU
        Rsp checkRes = payOrderService.checkSku(request.getSkuId());
        if (!checkRes.isSuccess()) {
            return checkRes;
        }

        // 初始化订单信息
        String userId = request.getUserId();
        String tenantCode = request.getTenantCode();
        String orderKey = IdWorker.nextAutoIdStr();
        String sessionKey = orderKey;
        PayGoodsSku sku = (PayGoodsSku) checkRes.getData();
        Integer salePrice = calculateSalePrice(request, sku);

        // 校验优惠信息、计算抵扣金额
        Rsp rebateCheckRes = validateRebate(request, tenantCode, userId, salePrice);
        if (!rebateCheckRes.isSuccess()) {
            return rebateCheckRes;
        }
        salePrice = (Integer) rebateCheckRes.getData();

        if (salePrice <= 0) {
            log.error("非法订单金额：{} | request={}", salePrice, request);
            return BaseRspUtils.createErrorRsp("非法订单金额，请联系客服");
        }

        // 构建订单请求
        ProduceOrderRequest produceOrderRequest = ProduceOrderRequest.builder()
                .orderNo(orderKey)
                .amount(salePrice)
                .description(sku.getSkuDesc())
                .build();

        sessionKey = this.prepareOrderRequest(request, produceOrderRequest, sessionKey);

        // 生成订单
        Rsp response = payHelperFactory.produce(produceOrderRequest, request.getPayType());
        if (response.isSuccess()) {
            request.setOrderNo(orderKey);
            this.cacheOrder(request, sku, orderKey);
            return BaseRspUtils.createSuccessRsp(PayCreateOrderRspBO.builder()
                    .payType(request.getPayType())
                    .orderNo(orderKey)
                    .payUrl(response.getData().toString())
                    .sessionKey(sessionKey)
                    .build());
        }
        return response;
    }

    private Integer calculateSalePrice(PayCreateOrderReqBO request, PayGoodsSku sku) {
        Integer salePrice = sku.getSalePrice();
        if (ObjectUtils.isNotEmpty(request.getPrice()) && TOKEN.equals(request.getToken())) {
            salePrice = request.getPrice();
        }
        return salePrice;
    }

    /**
     * 用户 优惠券、积分 二选一
     *
     * @param request
     * @param tenantCode
     * @param userId
     * @param salePrice
     * @return
     */
    private Rsp validateRebate(PayCreateOrderReqBO request, String tenantCode, String userId, Integer salePrice) {
        if (ObjectUtils.isNotEmpty(request.getCouponId())) {
            Rsp<UserCouponResBO> couponRsp = rebatePayApi.checkCoupon(tenantCode, userId, request.getCouponId());
            if (!couponRsp.isSuccess()) {
                return couponRsp;
            }
            salePrice -= couponRsp.getData().getAmount();
        } else if (ObjectUtils.isNotEmpty(request.getDiscountPoints())) {
            UserInfoReqBO infoReqBO = new UserInfoReqBO();
            infoReqBO.setTenantCode(tenantCode);
            infoReqBO.setUserId(userId);
            infoReqBO.setAmount(salePrice);
            Rsp<BalanceResBO> balance = rebatePayApi.balance(infoReqBO);
            if (!balance.isSuccess()) {
                return balance;
            }
            salePrice -= balance.getData().getDeductionAmount();
        }
        return BaseRspUtils.createSuccessRsp(salePrice);
    }

    private void cacheOrder(PayCreateOrderReqBO request, PayGoodsSku sku, String orderKey) {
        PayOrderBO cacheOrder = payOrderService.buildCacheOrder(request, sku);
        redisHelper.set(RedisKeyUtils.getOrderKey(orderKey), cacheOrder, 24 * 60 * 60); // 缓存一天
    }

    public String prepareOrderRequest(PayCreateOrderReqBO request, ProduceOrderRequest produceOrderRequest, String sessionKey) {
        if (this.isPromo(request.getSkuId())) {
            if (StringUtils.isEmpty(request.getSessionKey())) {
                produceOrderRequest.setAttach(AttachDataHelper.addData(OrderTypeEnums.recharge.getCode(), sessionKey));
            } else {
                sessionKey = request.getSessionKey();
                produceOrderRequest.setAttach(AttachDataHelper.addData(OrderTypeEnums.recharge.getCode(), sessionKey));
                // 获取活动价格
                Integer promotionPrice = promotionService.pullPromotionPrice(sessionKey);
                if (ObjectUtils.isEmpty(promotionPrice)) {
                    log.warn("活动机会已用完:{}", request.getUserId());
                } else {
                    produceOrderRequest.setAmount(promotionPrice);
                }
            }
        } else {
            produceOrderRequest.setAttach(AttachDataHelper.addData(OrderTypeEnums.recharge.getCode()));
        }
        return sessionKey;
    }

    /**
     * 判断是否活动商品
     */
    public Boolean isPromo(String skuId) {
        String key = RedisKeyUtils.getPromotionPriceKey(skuId);
        return redisHelper.hasKey(key);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp refundOrder(PayCreateOrderReqBO request) {
        log.info("退款订单：{}", request);

        //退还现金
        Rsp<PayTradeRecord> rsp = this.refundCash(request);

        if (!request.getOnlyRefund() && rsp.isSuccess()) {
            String userId = rsp.getData().getUserId();
            String tenantCode = rsp.getData().getTenantCode();

            //回收权益
            refundOrderService.reclaim(tenantCode, userId, request.getOrderNo());

            //删除缓存
            redisHelper.del(RedisConstants.USER_VIP_INFO_PREFIX + tenantCode + ":" + userId);
            redisHelper.del(RedisConstants.USER_INFO_KEY_PREFIX + userId);
        }
        return BaseRspUtils.createSuccessRsp("退款申请发起成功");
    }

    /**
     * 退现金
     *
     * @param request
     */
    private Rsp refundCash(PayCreateOrderReqBO request) {
        Rsp<PayTradeRecord> rsp = payOrderService.queryTrade(request.getOrderNo());
        if (!rsp.isSuccess()) {
            return rsp;
        }
        PayTradeRecord originTrade = rsp.getData();
        PayTradeRecord refundTrade = payOrderService.createRefundTrade(originTrade.getTradeNo(), request.getOrderNo(), request.getUserId(), request.getRemark());

        String orderNo = originTrade.getOrderNo();
        String refundTradeNo = refundTrade.getTradeNo();

        PayTradeRecord update = PayTradeRecord.builder().tradeNo(originTrade.getTradeNo()).refundNo(refundTradeNo).build();
        payOrderService.updateTradeRecord(update);

        ProduceOrderRequest refundOrder = ProduceOrderRequest.builder().refundNo(refundTradeNo).description("退款").orderNo(orderNo).amount(originTrade.getPayPrice()).build();
        Rsp<RefundDTO> response = payHelperFactory.refund(refundOrder, originTrade.getPayType());
        if (!response.isSuccess()) {
            return BaseRspUtils.createErrorRsp("退款申请发起失败");
        }
        RefundDTO refund = response.getData();
        PayTradeRecord updateRefund = PayTradeRecord.builder().tradeNo(refundTradeNo).payStatus(refund.getStatus()).payDesc(refund.getStatus()).payPrice(Math.toIntExact(refund.getRefund())) // 退款金额
                .notifyContent(refund.getNotifyContent()).payNo(refund.getPayNo()).build();
        if (refund.getCreateTime().contains("T")) {
            updateRefund.setPayTime(DateTimeUtil.convertAsDate(refund.getCreateTime(), "yyyy-MM-dd'T'HH:mm:ss+08:00"));
        } else {
            updateRefund.setPayTime(DateTimeUtil.convertAsDate(refund.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        payOrderService.updateTradeRecord(updateRefund);

        // 异步去查询退款状态，防止支付宝慢导致失败
        if ("POLL".equals(refund.getStatus())) {
            aliNotifyService.pollRefund(refundTradeNo, orderNo);
        }
        return rsp;
    }

    @Override
    public Rsp queryOrder(PayQueryOrderReqBO request) {
        log.info("查询订单：{}", request);
        Object o = redisHelper.get(RedisKeyUtils.getOrderKey(request.getOrderNo()));
        if (ObjectUtils.isNotEmpty(o)) {
            return BaseRspUtils.createSuccessRsp(o);
        }
        return payOrderService.queryOrder(request.getOrderNo());
    }

    @Override
    public RspList<PayOrderCountBO> queryMyOrder(PayQueryOrderReqBO request) {
        log.info("查询我的订单：{}", request);
        int page = request.getPage();
        int limit = request.getLimit();
        RspList<PayOrderCountBO> rspList = queryPayOrderCount(request, PayBusiType.ALL);
        List<PayOrderCountBO> combinedList = rspList != null && rspList.getRows() != null ? rspList.getRows().stream().sorted(Comparator.comparing(PayOrderCountBO::getOrderTime).reversed()).collect(Collectors.toList()) : new ArrayList<>();

        if (CollectionUtils.isEmpty(combinedList)) {
            return BaseRspUtils.createSuccessRspList(combinedList, 0);
        }
        int total = combinedList.size();
        List<PayOrderCountBO> paginatedList = combinedList;
        if (Boolean.TRUE.equals(request.getIsPaged())) {
            // 手动分页
            paginatedList = combinedList.stream().skip((long) (page - 1) * limit) // 跳过前面页的数据
                    .limit(limit) // 限制当前页的大小
                    .collect(Collectors.toList());
        }

        // 查询开票状态
        // 查询订单的发票状态，转换为 Map<String, String>
        Map<String, String> orderInvoiceStatusMap = payInvoiceRecordMapper.selectOrderInvoiceStatus(paginatedList.stream().map(PayOrderCountBO::getOrderNo).collect(Collectors.toList())).stream().collect(Collectors.toMap(
                map -> map.get("order_id"), // 取出 Map 中的 order_id 作为键
                map -> map.get("invoice_status") // 取出 Map 中的 invoice_status 作为值
        ));

        // 遍历分页列表，设置发票状态
        paginatedList.forEach(order -> {
            if (OrderStatusEnum.REFUND.getCode().equals(order.getOrderStatus())) {
                order.setInvoiceStatus(InvoiceStatusType.REFUNDED.getCode());
            } else {
                // 获取对应的 invoiceStatus，如果不存在则使用默认值
                String invoiceStatus = orderInvoiceStatusMap.getOrDefault(order.getOrderNo(), InvoiceStatusType.NOT_INVOICED.getCode());
                order.setInvoiceStatus(invoiceStatus);
            }
        });
        return BaseRspUtils.createSuccessRspList(paginatedList, total);
    }

    private RspList<PayOrderCountBO> queryPayOrderCount(PayQueryOrderReqBO request, PayBusiType busiType) {
        PayOrderCountReqBO payOrderCountReqBO = new PayOrderCountReqBO();
        payOrderCountReqBO.setTargetUserId(request.getUserId());
        payOrderCountReqBO.setTargetTenant(request.getTenantCode());
        payOrderCountReqBO.setBusiType(busiType.getCode());
        payOrderCountReqBO.setIsPaged(false);
        return payOpDataCountApi.countPayDataList(payOrderCountReqBO);
    }

}
