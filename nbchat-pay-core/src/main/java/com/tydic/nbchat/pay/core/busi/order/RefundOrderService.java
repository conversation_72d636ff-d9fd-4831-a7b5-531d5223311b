package com.tydic.nbchat.pay.core.busi.order;

import com.tydic.nbchat.pay.mapper.NameMapper;
import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class RefundOrderService {

    @Resource
    NameMapper nameMapper;


    /* 回收权益
        1. 扣豆
        2. 充值任务失效
        3. 删除积分明细
        不是加油包
        3. 更新执行时间
        4. 失效用户vip
     */
    public void reclaim(String tenantCode, String userId, String orderNo) {
        Integer score = nameMapper.countGiveDou(orderNo);

        this.deductFromBalance(tenantCode, userId, score);
        this.saveTrade(tenantCode, userId, score);
        this.invalidRechargeTask(orderNo);
        this.deleteScoreDetail(orderNo);

        if (!isScorePackage(orderNo)) {
            this.updateExecTime(userId, orderNo);
            this.updateUserVip(userId, orderNo);
        }
    }

    private void deductFromBalance(String tenantCode, String userId, Integer score) {
        log.info("从用户余额中扣除迪豆，用户ID:{}, 迪豆数量:{}", userId, score);
        nameMapper.deductFromBalance(tenantCode, userId, score);
    }

    private void invalidRechargeTask(String orderNo) {
        log.info("充值任务失效，订单ID:{}", orderNo);
        nameMapper.deleteRechargeTask(orderNo);
    }

    private void deleteScoreDetail(String orderNo) {
        log.info("删除积分明细，订单ID:{}", orderNo);
        nameMapper.deleteScoreDetail(orderNo);
    }

    private void updateExecTime(String userId, String orderNo) {
        log.info("更新执行时间，订单ID:{}, 用户ID:{}", orderNo, userId);
        nameMapper.updateExecTime(orderNo, userId);
    }

    private void updateUserVip(String userId, String orderNo) {
        log.info("更新用户VIP，用户ID:{}，退款订单：{} ", userId, orderNo);
        nameMapper.invalidVipType(orderNo, userId);
    }

    public Boolean isScorePackage(String orderNo) {
        String scoreType = nameMapper.queryScoreType(orderNo);
        if (StringUtils.isEmpty(scoreType)) {
            return true;
        }
        return UserVipType.SCORE_PACKAGE.getCode().equals(scoreType);
    }

    public void saveTrade(String tenantCode, String userId, Integer score) {
        nameMapper.saveTrade(tenantCode, userId, score);
    }
}
